<?php
require_once '../includes/dbh.inc.php';

echo "<h3>Fixing Subjects Table...</h3>";

// Check if department_id column exists
$checkDeptIdColumnQuery = "SHOW COLUMNS FROM subjects LIKE 'department_id'";
$deptIdColumnResult = $conn->query($checkDeptIdColumnQuery);

if ($deptIdColumnResult->num_rows == 0) {
    echo "Adding department_id column to subjects table...<br>";
    $addDeptIdColumnQuery = "ALTER TABLE subjects ADD COLUMN department_id INT(11) NULL";
    if ($conn->query($addDeptIdColumnQuery)) {
        echo "✅ department_id column added successfully!<br>";
    } else {
        echo "❌ Error adding department_id column: " . $conn->error . "<br>";
    }
} else {
    echo "✅ department_id column already exists!<br>";
}

// Check if is_active column exists
$checkActiveColumnQuery = "SHOW COLUMNS FROM subjects LIKE 'is_active'";
$activeColumnResult = $conn->query($checkActiveColumnQuery);

if ($activeColumnResult->num_rows == 0) {
    echo "Adding is_active column to subjects table...<br>";
    $addActiveColumnQuery = "ALTER TABLE subjects ADD COLUMN is_active TINYINT(1) DEFAULT 1";
    if ($conn->query($addActiveColumnQuery)) {
        echo "✅ is_active column added successfully!<br>";
    } else {
        echo "❌ Error adding is_active column: " . $conn->error . "<br>";
    }
} else {
    echo "✅ is_active column already exists!<br>";
}

// Show current table structure
echo "<h3>Current Subjects Table Structure:</h3>";
$result = $conn->query("DESCRIBE subjects");
if ($result) {
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['Field'] . "</td>";
        echo "<td>" . $row['Type'] . "</td>";
        echo "<td>" . $row['Null'] . "</td>";
        echo "<td>" . $row['Key'] . "</td>";
        echo "<td>" . $row['Default'] . "</td>";
        echo "<td>" . $row['Extra'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
}

// Check if subject_categories table exists first
echo "<h3>Fixing Subject Categories Table...</h3>";
$checkTableQuery = "SHOW TABLES LIKE 'subject_categories'";
$tableResult = $conn->query($checkTableQuery);

if ($tableResult && $tableResult->num_rows > 0) {
    // Check if department_id column exists in subject_categories table
    $checkCatDeptColumnQuery = "SHOW COLUMNS FROM subject_categories LIKE 'department_id'";
    $catDeptColumnResult = $conn->query($checkCatDeptColumnQuery);

    if ($catDeptColumnResult && $catDeptColumnResult->num_rows == 0) {
        echo "Adding department_id column to subject_categories table...<br>";
        $addCatDeptColumnQuery = "ALTER TABLE subject_categories ADD COLUMN department_id INT(11) NULL";
        if ($conn->query($addCatDeptColumnQuery)) {
            echo "✅ department_id column added to subject_categories successfully!<br>";
        } else {
            echo "❌ Error adding department_id column to subject_categories: " . $conn->error . "<br>";
        }
    } else {
        echo "✅ department_id column already exists in subject_categories!<br>";
    }

    // Check if is_active column exists in subject_categories table
    $checkCatActiveColumnQuery = "SHOW COLUMNS FROM subject_categories LIKE 'is_active'";
    $catActiveColumnResult = $conn->query($checkCatActiveColumnQuery);

    if ($catActiveColumnResult && $catActiveColumnResult->num_rows == 0) {
        echo "Adding is_active column to subject_categories table...<br>";
        $addCatActiveColumnQuery = "ALTER TABLE subject_categories ADD COLUMN is_active TINYINT(1) DEFAULT 1";
        if ($conn->query($addCatActiveColumnQuery)) {
            echo "✅ is_active column added to subject_categories successfully!<br>";
        } else {
            echo "❌ Error adding is_active column to subject_categories: " . $conn->error . "<br>";
        }
    } else {
        echo "✅ is_active column already exists in subject_categories!<br>";
    }
} else {
    echo "⚠️ subject_categories table doesn't exist yet. It will be created when you visit the page.<br>";
}

echo "<br><a href='subject_categories.php'>Go to Subject Categories</a>";
?>
