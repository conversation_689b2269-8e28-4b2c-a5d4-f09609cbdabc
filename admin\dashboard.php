<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Get stats for dashboard
$statsQuery = [
    'students' => "SELECT COUNT(*) as count FROM students",
    'teachers' => "SELECT COUNT(*) as count FROM teachers",
    'staff' => "SELECT COUNT(*) as count FROM staff",
    'classes' => "SELECT COUNT(*) as count FROM classes",
    'subjects' => "SELECT COUNT(*) as count FROM subjects WHERE is_active = 1 OR is_active IS NULL",
    'categories' => "SELECT COUNT(*) as count FROM subject_categories WHERE is_active = 1 OR is_active IS NULL",
    'departments' => "SELECT COUNT(*) as count FROM departments"
];

$stats = [];
foreach ($statsQuery as $key => $query) {
    $result = $conn->query($query);
    if ($result) {
        $row = $result->fetch_assoc();
        $stats[$key] = $row['count'];
    } else {
        $stats[$key] = 0;
    }
}

// Get recent students
$recentStudentsQuery = "SELECT s.student_id, s.first_name, s.last_name, d.department_name, s.admission_date 
                      FROM students s 
                      LEFT JOIN departments d ON s.department_id = d.id
                      ORDER BY s.id DESC 
                      LIMIT 5";
$recentStudents = $conn->query($recentStudentsQuery);

// Get recent teachers
$recentTeachersQuery = "SELECT t.teacher_id, t.first_name, t.last_name, d.department_name, t.joining_date 
                      FROM teachers t 
                      LEFT JOIN departments d ON t.department_id = d.id
                      ORDER BY t.id DESC 
                      LIMIT 5";
$recentTeachers = $conn->query($recentTeachersQuery);

// Courses table has been removed, so set upcomingExams to null
$upcomingExams = null;

// Old query that referenced the courses table
// $upcomingExamsQuery = "SELECT e.exam_name, c.course_name, e.exam_date, e.total_marks
//                      FROM exams e
//                      JOIN courses c ON e.course_id = c.id
//                      WHERE e.exam_date >= CURDATE()
//                      ORDER BY e.exam_date ASC
//                      LIMIT 5";
// $upcomingExams = $conn->query($upcomingExamsQuery);
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ড্যাশবোর্ড - কলেজ ম্যানেজমেন্ট সিস্টেম</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar">
                <div class="text-center mb-4">
                    <h3>অ্যাডমিন প্যানেল</h3>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link active" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i> ড্যাশবোর্ড
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="students.php">
                            <i class="fas fa-user-graduate me-2"></i> শিক্ষার্থী
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="teachers.php">
                            <i class="fas fa-chalkboard-teacher me-2"></i> শিক্ষক
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="staff.php">
                            <i class="fas fa-user-tie me-2"></i> কর্মচারী
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="courses.php">
                            <i class="fas fa-book me-2"></i> কোর্স
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="classes.php">
                            <i class="fas fa-chalkboard me-2"></i> ক্লাস
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="departments.php">
                            <i class="fas fa-building me-2"></i> বিভাগ
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="subjects.php">
                            <i class="fas fa-book-open me-2"></i> বিষয়
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="subject_categories.php">
                            <i class="fas fa-tags me-2"></i> বিষয় ক্যাটাগরি
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="student_subject_selection.php">
                            <i class="fas fa-list-check me-2"></i> শিক্ষার্থী বিষয় নির্বাচন
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="exams.php">
                            <i class="fas fa-file-alt me-2"></i> পরীক্ষা
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="results.php">
                            <i class="fas fa-chart-bar me-2"></i> ফলাফল
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="fees.php">
                            <i class="fas fa-money-bill-wave me-2"></i> ফি ম্যানেজমেন্ট
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="fee_reports.php">
                            <i class="fas fa-chart-pie me-2"></i> ফি রিপোর্ট
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reports.php">
                            <i class="fas fa-file-pdf me-2"></i> রিপোর্ট
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="settings.php">
                            <i class="fas fa-cog me-2"></i> সেটিংস
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="users.php">
                            <i class="fas fa-users me-2"></i> ইউজার ম্যানেজমেন্ট
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="attendance.php">
                            <i class="fas fa-calendar-check me-2"></i> উপস্থিতি
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="library.php">
                            <i class="fas fa-book-reader me-2"></i> লাইব্রেরি
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="hostel.php">
                            <i class="fas fa-hotel me-2"></i> হোস্টেল
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="transport.php">
                            <i class="fas fa-bus me-2"></i> পরিবহন
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="events.php">
                            <i class="fas fa-calendar-day me-2"></i> ইভেন্ট
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="notices.php">
                            <i class="fas fa-bullhorn me-2"></i> নোটিশ
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="announcements.php">
                            <i class="fas fa-bell me-2"></i> ঘোষণা
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../includes/logout.inc.php">
                            <i class="fas fa-sign-out-alt me-2"></i> লগআউট
                        </a>
                    </li>
                </ul>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <div class="row mb-4">
                    <div class="col">
                        <h2>ড্যাশবোর্ড</h2>
                        <p class="text-muted">সিস্টেমের সামগ্রিক পরিসংখ্যান এবং দ্রুত অ্যাক্সেস</p>
                    </div>
                </div>
                
                <!-- Statistics Cards -->
                <div class="row mb-4">
                    <div class="col-lg-3 col-md-6 mb-4">
                        <div class="card stat-card bg-primary text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="card-title">মোট শিক্ষার্থী</h6>
                                        <h2 class="mb-0"><?php echo $stats['students'] ?? 0; ?></h2>
                                    </div>
                                    <i class="fas fa-user-graduate fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-4">
                        <div class="card stat-card bg-success text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="card-title">মোট শিক্ষক</h6>
                                        <h2 class="mb-0"><?php echo $stats['teachers'] ?? 0; ?></h2>
                                    </div>
                                    <i class="fas fa-chalkboard-teacher fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-4">
                        <div class="card stat-card bg-info text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="card-title">মোট বিষয়</h6>
                                        <h2 class="mb-0"><?php echo $stats['subjects'] ?? 0; ?></h2>
                                    </div>
                                    <i class="fas fa-book-open fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-4">
                        <div class="card stat-card bg-warning text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="card-title">মোট বিভাগ</h6>
                                        <h2 class="mb-0"><?php echo $stats['departments'] ?? 0; ?></h2>
                                    </div>
                                    <i class="fas fa-building fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Additional Statistics Cards -->
                <div class="row mb-4">
                    <div class="col-lg-3 col-md-6 mb-4">
                        <div class="card stat-card bg-secondary text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="card-title">মোট কর্মচারী</h6>
                                        <h2 class="mb-0"><?php echo $stats['staff'] ?? 0; ?></h2>
                                    </div>
                                    <i class="fas fa-user-tie fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-4">
                        <div class="card stat-card bg-dark text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="card-title">মোট ক্লাস</h6>
                                        <h2 class="mb-0"><?php echo $stats['classes'] ?? 0; ?></h2>
                                    </div>
                                    <i class="fas fa-chalkboard fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-4">
                        <div class="card stat-card bg-purple text-white" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="card-title">বিষয় ক্যাটাগরি</h6>
                                        <h2 class="mb-0"><?php echo $stats['categories'] ?? 0; ?></h2>
                                    </div>
                                    <i class="fas fa-tags fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-4">
                        <div class="card stat-card text-white" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%) !important;">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="card-title">সিস্টেম স্ট্যাটাস</h6>
                                        <h6 class="mb-0">সক্রিয়</h6>
                                    </div>
                                    <i class="fas fa-check-circle fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row mt-4">
                    <!-- Recent Students -->
                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header bg-light">
                                <h5 class="card-title mb-0">সাম্প্রতিক শিক্ষার্থী</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>নাম</th>
                                                <th>আইডি</th>
                                                <th>বিভাগ</th>
                                                <th>তারিখ</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php if ($recentStudents && $recentStudents->num_rows > 0): ?>
                                                <?php while ($student = $recentStudents->fetch_assoc()): ?>
                                                    <tr>
                                                        <td><?php echo htmlspecialchars($student['first_name'] . ' ' . $student['last_name']); ?></td>
                                                        <td><?php echo htmlspecialchars($student['student_id']); ?></td>
                                                        <td><?php echo htmlspecialchars($student['department_name'] ?? 'N/A'); ?></td>
                                                        <td><?php echo date('d/m/Y', strtotime($student['admission_date'])); ?></td>
                                                    </tr>
                                                <?php endwhile; ?>
                                            <?php else: ?>
                                                <tr>
                                                    <td colspan="4" class="text-center">কোন সাম্প্রতিক শিক্ষার্থী পাওয়া যায়নি</td>
                                                </tr>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>
                                <a href="students.php" class="btn btn-primary btn-sm mt-3">সকল শিক্ষার্থী দেখান</a>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Recent Teachers -->
                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header bg-light">
                                <h5 class="card-title mb-0">সাম্প্রতিক শিক্ষক</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>নাম</th>
                                                <th>আইডি</th>
                                                <th>বিভাগ</th>
                                                <th>তারিখ</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php if ($recentTeachers && $recentTeachers->num_rows > 0): ?>
                                                <?php while ($teacher = $recentTeachers->fetch_assoc()): ?>
                                                    <tr>
                                                        <td><?php echo htmlspecialchars($teacher['first_name'] . ' ' . $teacher['last_name']); ?></td>
                                                        <td><?php echo htmlspecialchars($teacher['teacher_id']); ?></td>
                                                        <td><?php echo htmlspecialchars($teacher['department_name'] ?? 'N/A'); ?></td>
                                                        <td><?php echo date('d/m/Y', strtotime($teacher['joining_date'])); ?></td>
                                                    </tr>
                                                <?php endwhile; ?>
                                            <?php else: ?>
                                                <tr>
                                                    <td colspan="4" class="text-center">কোন সাম্প্রতিক শিক্ষক পাওয়া যায়নি</td>
                                                </tr>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>
                                <a href="teachers.php" class="btn btn-success btn-sm mt-3">সকল শিক্ষক দেখান</a>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <!-- Upcoming Exams -->
                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header bg-light">
                                <h5 class="card-title mb-0">পরীক্ষা</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>পরীক্ষা</th>
                                                <th>কোর্স</th>
                                                <th>তারিখ</th>
                                                <th>মার্ক</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php if ($upcomingExams && $upcomingExams->num_rows > 0): ?>
                                                <?php while ($exam = $upcomingExams->fetch_assoc()): ?>
                                                    <tr>
                                                        <td><?php echo htmlspecialchars($exam['exam_name']); ?></td>
                                                        <td><?php echo htmlspecialchars($exam['course_name']); ?></td>
                                                        <td><?php echo date('d/m/Y', strtotime($exam['exam_date'])); ?></td>
                                                        <td><?php echo htmlspecialchars($exam['total_marks']); ?></td>
                                                    </tr>
                                                <?php endwhile; ?>
                                            <?php else: ?>
                                                <tr>
                                                    <td colspan="4" class="text-center">কোন পরীক্ষা পাওয়া যায়নি</td>
                                                </tr>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>
                                <a href="exams.php" class="btn btn-warning btn-sm mt-3">সকল পরীক্ষা দেখান</a>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Quick Access Cards -->
                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header bg-light">
                                <h5 class="card-title mb-0">দ্রুত অ্যাক্সেস</h5>
                            </div>
                            <div class="card-body">
                                <div class="d-flex flex-wrap gap-2">
                                    <a href="add_student.php" class="btn btn-primary">
                                        <i class="fas fa-user-plus me-2"></i>নতুন শিক্ষার্থী যোগ করুন
                                    </a>
                                    <a href="add_teacher.php" class="btn btn-success">
                                        <i class="fas fa-chalkboard-teacher me-2"></i>নতুন শিক্ষক যোগ করুন
                                    </a>
                                    <a href="subject_categories.php" class="btn btn-info">
                                        <i class="fas fa-tags me-2"></i>বিষয় ক্যাটাগরি
                                    </a>
                                    <a href="student_subject_selection.php" class="btn btn-warning">
                                        <i class="fas fa-list-check me-2"></i>বিষয় নির্বাচন
                                    </a>
                                    <a href="departments.php" class="btn btn-secondary">
                                        <i class="fas fa-building me-2"></i>বিভাগ ব্যবস্থাপনা
                                    </a>
                                    <a href="fee_assign.php" class="btn btn-danger">
                                        <i class="fas fa-money-bill-wave me-2"></i>ফি এসাইন করুন
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- System Management -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card card-hover-effect">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="fas fa-cogs me-2"></i>সিস্টেম ম্যানেজমেন্ট</h5>
                            </div>
                            <div class="card-body system-management">
                                <div class="list-group list-group-flush">
                                    <a href="subjects.php" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                        <div>
                                            <i class="fas fa-book-open me-2 text-primary"></i>
                                            <strong>বিষয় ব্যবস্থাপনা</strong>
                                            <br><small class="text-muted">নতুন বিষয় যোগ, সম্পাদনা ও মুছুন</small>
                                        </div>
                                        <i class="fas fa-chevron-right"></i>
                                    </a>
                                    <a href="subject_categories.php" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                        <div>
                                            <i class="fas fa-tags me-2 text-info"></i>
                                            <strong>বিষয় ক্যাটাগরি</strong>
                                            <br><small class="text-muted">বিষয়ের ক্যাটাগরি তৈরি ও ব্যবস্থাপনা</small>
                                        </div>
                                        <i class="fas fa-chevron-right"></i>
                                    </a>
                                    <a href="departments.php" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                        <div>
                                            <i class="fas fa-building me-2 text-secondary"></i>
                                            <strong>বিভাগ ব্যবস্থাপনা</strong>
                                            <br><small class="text-muted">বিভাগ তৈরি ও সংগঠন</small>
                                        </div>
                                        <i class="fas fa-chevron-right"></i>
                                    </a>
                                    <a href="student_subject_selection.php" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                        <div>
                                            <i class="fas fa-list-check me-2 text-warning"></i>
                                            <strong>শিক্ষার্থী বিষয় নির্বাচন</strong>
                                            <br><small class="text-muted">শিক্ষার্থীদের বিষয় নির্বাচন ব্যবস্থাপনা</small>
                                        </div>
                                        <i class="fas fa-chevron-right"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card card-hover-effect">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="fas fa-chart-line me-2"></i>সাম্প্রতিক কার্যক্রম</h5>
                            </div>
                            <div class="card-body">
                                <div class="timeline">
                                    <div class="timeline-item">
                                        <div class="timeline-marker bg-primary"></div>
                                        <div class="timeline-content">
                                            <h6 class="mb-1">বিষয় ক্যাটাগরি সিস্টেম</h6>
                                            <p class="mb-0 text-muted">নতুন বিষয় ক্যাটাগরি ব্যবস্থাপনা সিস্টেম যোগ করা হয়েছে</p>
                                            <small class="text-muted">আজ</small>
                                        </div>
                                    </div>
                                    <div class="timeline-item">
                                        <div class="timeline-marker bg-success"></div>
                                        <div class="timeline-content">
                                            <h6 class="mb-1">শিক্ষার্থী বিষয় নির্বাচন</h6>
                                            <p class="mb-0 text-muted">উন্নত বিষয় নির্বাচন সিস্টেম চালু করা হয়েছে</p>
                                            <small class="text-muted">আজ</small>
                                        </div>
                                    </div>
                                    <div class="timeline-item">
                                        <div class="timeline-marker bg-info"></div>
                                        <div class="timeline-content">
                                            <h6 class="mb-1">বিভাগ ব্যবস্থাপনা</h6>
                                            <p class="mb-0 text-muted">বিভাগ অনুযায়ী বিষয় সংগঠন উন্নত করা হয়েছে</p>
                                            <small class="text-muted">গতকাল</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>



    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>