<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Get student ID from URL if provided
$studentId = isset($_GET['student_id']) ? $_GET['student_id'] : (isset($_GET['id']) ? $_GET['id'] : null);
$studentInfo = null;

// Get departments for filter
$departmentsQuery = "SELECT * FROM departments ORDER BY department_name";
$departments = $conn->query($departmentsQuery);

// Get current sessions
$sessionsQuery = "SELECT * FROM sessions ORDER BY session_name DESC";
$sessions = $conn->query($sessionsQuery);

// Get current session (latest)
$currentSessionQuery = "SELECT id, session_name FROM sessions ORDER BY id DESC LIMIT 1";
$currentSession = $conn->query($currentSessionQuery)->fetch_assoc();

// Handle subject selection form submission
$successMessage = '';
$errorMessage = '';

if (isset($_POST['save_selection']) && isset($_POST['student_id'])) {
    $student_db_id = $_POST['student_id'];
    $requiredSubjectIds = isset($_POST['required_subjects']) ? $_POST['required_subjects'] : [];
    $optionalSubjectIds = isset($_POST['optional_subjects']) ? $_POST['optional_subjects'] : [];
    $fourthSubjectIds = isset($_POST['fourth_subjects']) ? $_POST['fourth_subjects'] : [];
    $sessionId = $_POST['session_id'] ?? $currentSession['id'];
    
    $totalSelected = count($requiredSubjectIds) + count($optionalSubjectIds) + count($fourthSubjectIds);
    
    if ($totalSelected != 7) {
        $errorMessage = "ঠিক ৭টি বিষয় নির্বাচন করতে হবে। বর্তমানে {$totalSelected}টি নির্বাচন করা হয়েছে।";
    } elseif (count($fourthSubjectIds) > 1) {
        $errorMessage = "একটির বেশি ৪র্থ বিষয় নির্বাচন করা যাবে না।";
    } else {
        // Begin transaction
        $conn->begin_transaction();
        
        try {
            // Delete existing selections
            $deleteQuery = "DELETE FROM student_subjects WHERE student_id = ?";
            $stmt = $conn->prepare($deleteQuery);
            $stmt->bind_param("i", $student_db_id);
            $stmt->execute();
            
            // Insert required subjects
            $insertQuery = "INSERT INTO student_subjects (student_id, subject_id, category, session_id) VALUES (?, ?, ?, ?)";
            $stmt = $conn->prepare($insertQuery);
            
            foreach ($requiredSubjectIds as $subjectId) {
                $category = 'required';
                $stmt->bind_param("iisi", $student_db_id, $subjectId, $category, $sessionId);
                $stmt->execute();
            }
            
            // Insert optional subjects
            foreach ($optionalSubjectIds as $subjectId) {
                $category = 'optional';
                $stmt->bind_param("iisi", $student_db_id, $subjectId, $category, $sessionId);
                $stmt->execute();
            }
            
            // Insert fourth subjects
            foreach ($fourthSubjectIds as $subjectId) {
                $category = 'fourth';
                $stmt->bind_param("iisi", $student_db_id, $subjectId, $category, $sessionId);
                $stmt->execute();
            }
            
            // Commit transaction
            $conn->commit();
            $successMessage = "শিক্ষার্থীর বিষয় নির্বাচন সফলভাবে সংরক্ষণ করা হয়েছে!";
            
        } catch (Exception $e) {
            // Rollback on error
            $conn->rollback();
            $errorMessage = "বিষয় নির্বাচন সংরক্ষণ করতে সমস্যা হয়েছে: " . $e->getMessage();
        }
    }
}

// Handle student search
if (isset($_POST['search_student']) || isset($_GET['student_id']) || !empty($studentId)) {
    $searchId = '';
    if (isset($_POST['student_id_search'])) {
        $searchId = $_POST['student_id_search'];
    } elseif (isset($_GET['student_id'])) {
        $searchId = $_GET['student_id'];
    } elseif (!empty($studentId)) {
        $searchId = $studentId;
    }

    if (!empty($searchId)) {
        // Get student information
        $studentQuery = "SELECT s.*, d.department_name, c.class_name, ss.session_name
                         FROM students s
                         LEFT JOIN departments d ON s.department_id = d.id
                         LEFT JOIN classes c ON s.class_id = c.id
                         LEFT JOIN sessions ss ON s.session_id = ss.id
                         WHERE s.student_id = ? OR s.id = ?";
        $stmt = $conn->prepare($studentQuery);
        $stmt->bind_param("si", $searchId, $searchId);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows > 0) {
            $studentInfo = $result->fetch_assoc();
        } else {
            $error = "শিক্ষার্থী পাওয়া যায়নি।";
        }
    }
}

// Load subjects if student is found
if ($studentInfo) {
    // Get required subjects for student's department
            $requiredSubjectsQuery = "SELECT s.id, s.subject_name, s.subject_code, s.category
                                    FROM subjects s
                                    WHERE s.department_id = ?
                                    AND (s.is_active = 1 OR s.is_active IS NULL)
                                    AND (s.category = 'required' OR s.category LIKE '%required%')
                                    ORDER BY s.subject_name";
            $stmt = $conn->prepare($requiredSubjectsQuery);
            $stmt->bind_param("i", $studentInfo['department_id']);
            $stmt->execute();
            $requiredSubjects = $stmt->get_result();

            // Get optional subjects for student's department
            $optionalSubjectsQuery = "SELECT s.id, s.subject_name, s.subject_code, s.category
                                    FROM subjects s
                                    WHERE s.department_id = ?
                                    AND (s.is_active = 1 OR s.is_active IS NULL)
                                    AND (s.category = 'optional' OR s.category LIKE '%optional%')
                                    ORDER BY s.subject_name";
            $stmt = $conn->prepare($optionalSubjectsQuery);
            $stmt->bind_param("i", $studentInfo['department_id']);
            $stmt->execute();
            $optionalSubjects = $stmt->get_result();

            // Get fourth subjects for student's department
            $fourthSubjectsQuery = "SELECT s.id, s.subject_name, s.subject_code, s.category
                                   FROM subjects s
                                   WHERE s.department_id = ?
                                   AND (s.is_active = 1 OR s.is_active IS NULL)
                                   AND (s.category = 'fourth' OR s.category LIKE '%fourth%')
                                   ORDER BY s.subject_name";
            $stmt = $conn->prepare($fourthSubjectsQuery);
            $stmt->bind_param("i", $studentInfo['department_id']);
            $stmt->execute();
            $fourthSubjects = $stmt->get_result();

            // If no subjects found using category field, get all subjects and distribute them
            if ($requiredSubjects->num_rows == 0 && $optionalSubjects->num_rows == 0 && $fourthSubjects->num_rows == 0) {
                // First try to get all subjects for this department
                $allSubjectsQuery = "SELECT s.id, s.subject_name, s.subject_code, s.category
                                   FROM subjects s
                                   WHERE s.department_id = ?
                                   AND (s.is_active = 1 OR s.is_active IS NULL)
                                   ORDER BY s.subject_name";
                $stmt = $conn->prepare($allSubjectsQuery);
                $stmt->bind_param("i", $studentInfo['department_id']);
                $stmt->execute();
                $allSubjectsResult = $stmt->get_result();

                if ($allSubjectsResult->num_rows > 0) {
                    // Distribute all subjects across categories
                    $allSubjectsArray = [];
                    while ($subject = $allSubjectsResult->fetch_assoc()) {
                        $allSubjectsArray[] = $subject;
                    }

                    // Split into categories based on existing category or distribute evenly
                    $requiredSubjectsArray = [];
                    $optionalSubjectsArray = [];
                    $fourthSubjectsArray = [];

                    foreach ($allSubjectsArray as $subject) {
                        $category = strtolower($subject['category'] ?? '');
                        if (strpos($category, 'required') !== false) {
                            $requiredSubjectsArray[] = $subject;
                        } elseif (strpos($category, 'optional') !== false) {
                            $optionalSubjectsArray[] = $subject;
                        } elseif (strpos($category, 'fourth') !== false) {
                            $fourthSubjectsArray[] = $subject;
                        } else {
                            // Distribute evenly if no category
                            $totalCount = count($requiredSubjectsArray) + count($optionalSubjectsArray) + count($fourthSubjectsArray);
                            if ($totalCount % 3 == 0) {
                                $requiredSubjectsArray[] = $subject;
                            } elseif ($totalCount % 3 == 1) {
                                $optionalSubjectsArray[] = $subject;
                            } else {
                                $fourthSubjectsArray[] = $subject;
                            }
                        }
                    }

                    // Convert arrays to result objects
                    $requiredSubjects = (object)['num_rows' => count($requiredSubjectsArray), 'data' => $requiredSubjectsArray];
                    $optionalSubjects = (object)['num_rows' => count($optionalSubjectsArray), 'data' => $optionalSubjectsArray];
                    $fourthSubjects = (object)['num_rows' => count($fourthSubjectsArray), 'data' => $fourthSubjectsArray];
                } else {
                    // No subjects found for this department, try to get general subjects (department_id IS NULL)
                    $generalSubjectsQuery = "SELECT s.id, s.subject_name, s.subject_code, s.category
                                           FROM subjects s
                                           WHERE s.department_id IS NULL
                                           AND (s.is_active = 1 OR s.is_active IS NULL)
                                           ORDER BY s.subject_name";
                    $generalSubjectsResult = $conn->query($generalSubjectsQuery);

                    if ($generalSubjectsResult->num_rows > 0) {
                        $allSubjectsArray = [];
                        while ($subject = $generalSubjectsResult->fetch_assoc()) {
                            $allSubjectsArray[] = $subject;
                        }

                        // Distribute general subjects
                        $requiredSubjectsArray = [];
                        $optionalSubjectsArray = [];
                        $fourthSubjectsArray = [];

                        foreach ($allSubjectsArray as $subject) {
                            $category = strtolower($subject['category'] ?? '');
                            if (strpos($category, 'required') !== false) {
                                $requiredSubjectsArray[] = $subject;
                            } elseif (strpos($category, 'optional') !== false) {
                                $optionalSubjectsArray[] = $subject;
                            } elseif (strpos($category, 'fourth') !== false) {
                                $fourthSubjectsArray[] = $subject;
                            } else {
                                // Default to required
                                $requiredSubjectsArray[] = $subject;
                            }
                        }

                        $requiredSubjects = (object)['num_rows' => count($requiredSubjectsArray), 'data' => $requiredSubjectsArray];
                        $optionalSubjects = (object)['num_rows' => count($optionalSubjectsArray), 'data' => $optionalSubjectsArray];
                        $fourthSubjects = (object)['num_rows' => count($fourthSubjectsArray), 'data' => $fourthSubjectsArray];
                    }
                }
            }

            // If still no subjects, try using subject categories
            if ($requiredSubjects->num_rows == 0 && $optionalSubjects->num_rows == 0 && $fourthSubjects->num_rows == 0) {
                // Get subjects from subject categories
                $categorySubjectsQuery = "SELECT DISTINCT s.id, s.subject_name, s.subject_code, sc.category_name as category
                                         FROM subjects s
                                         JOIN subject_category_mapping scm ON s.id = scm.subject_id
                                         JOIN subject_categories sc ON scm.category_id = sc.id
                                         WHERE (s.department_id = ? OR s.department_id IS NULL OR scm.department_id = ?)
                                         AND (s.is_active = 1 OR s.is_active IS NULL)
                                         ORDER BY sc.category_name, s.subject_name";
                $stmt = $conn->prepare($categorySubjectsQuery);
                $stmt->bind_param("ii", $studentInfo['department_id'], $studentInfo['department_id']);
                $stmt->execute();
                $categorySubjects = $stmt->get_result();

                // Separate subjects by category
                $requiredSubjectsArray = [];
                $optionalSubjectsArray = [];
                $fourthSubjectsArray = [];

                while ($subject = $categorySubjects->fetch_assoc()) {
                    $categoryName = strtolower($subject['category']);
                    if (strpos($categoryName, 'required') !== false || strpos($categoryName, 'আবশ্যিক') !== false) {
                        $requiredSubjectsArray[] = $subject;
                    } elseif (strpos($categoryName, 'optional') !== false || strpos($categoryName, 'ঐচ্ছিক') !== false) {
                        $optionalSubjectsArray[] = $subject;
                    } elseif (strpos($categoryName, 'fourth') !== false || strpos($categoryName, '৪র্থ') !== false) {
                        $fourthSubjectsArray[] = $subject;
                    }
                }

                // Convert arrays to result objects (for compatibility)
                $requiredSubjects = (object)['num_rows' => count($requiredSubjectsArray), 'data' => $requiredSubjectsArray];
                $optionalSubjects = (object)['num_rows' => count($optionalSubjectsArray), 'data' => $optionalSubjectsArray];
                $fourthSubjects = (object)['num_rows' => count($fourthSubjectsArray), 'data' => $fourthSubjectsArray];
            }

            // Get student's selected subjects
            $selectedSubjectsQuery = "SELECT subject_id, category FROM student_subjects WHERE student_id = ?";
            $stmt = $conn->prepare($selectedSubjectsQuery);
            $stmt->bind_param("i", $studentInfo['id']);
            $stmt->execute();
            $selectedSubjectsResult = $stmt->get_result();
            
            $selectedRequiredIds = [];
            $selectedOptionalIds = [];
            $selectedFourthIds = [];
            
            if ($selectedSubjectsResult && $selectedSubjectsResult->num_rows > 0) {
                while ($subject = $selectedSubjectsResult->fetch_assoc()) {
                    if ($subject['category'] == 'required') {
                        $selectedRequiredIds[] = $subject['subject_id'];
                    } elseif ($subject['category'] == 'optional') {
                        $selectedOptionalIds[] = $subject['subject_id'];
                    } elseif ($subject['category'] == 'fourth') {
                        $selectedFourthIds[] = $subject['subject_id'];
                    }
                }
            }
        }
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>শিক্ষার্থী বিষয় নির্বাচন - অ্যাডমিন প্যানেল</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .subject-card {
            margin-bottom: 15px;
            transition: all 0.3s ease;
        }
        .subject-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .subject-card.selected {
            border-color: #28a745;
            background-color: rgba(40, 167, 69, 0.1);
        }
        .selection-summary {
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .counter-badge {
            font-size: 16px;
            padding: 5px 10px;
        }
        .student-info {
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar">
                <div class="text-center mb-4">
                    <h3>অ্যাডমিন প্যানেল</h3>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i> ড্যাশবোর্ড
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="students.php">
                            <i class="fas fa-user-graduate me-2"></i> শিক্ষার্থী
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="teachers.php">
                            <i class="fas fa-chalkboard-teacher me-2"></i> শিক্ষক
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="subjects.php">
                            <i class="fas fa-book-open me-2"></i> বিষয়
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="subject_categories.php">
                            <i class="fas fa-tags me-2"></i> বিষয় ক্যাটাগরি
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="student_subject_selection.php">
                            <i class="fas fa-list-check me-2"></i> বিষয় নির্বাচন
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../includes/logout.inc.php">
                            <i class="fas fa-sign-out-alt me-2"></i> লগআউট
                        </a>
                    </li>
                </ul>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <div class="row mb-4">
                    <div class="col">
                        <h2>শিক্ষার্থী বিষয় নির্বাচন</h2>
                        <p class="text-muted">অ্যাডমিন থেকে শিক্ষার্থীদের জন্য বিষয় নির্বাচন করুন</p>
                    </div>
                    <div class="col-auto">
                        <a href="subject_categories.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>বিষয় ক্যাটাগরি
                        </a>
                    </div>
                </div>



                <!-- Search Student Form -->
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">শিক্ষার্থী অনুসন্ধান</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="student_subject_selection.php" class="row g-3">
                            <div class="col-md-4">
                                <label for="student_id_search" class="form-label">শিক্ষার্থী আইডি</label>
                                <input type="text" class="form-control" id="student_id_search" name="student_id_search"
                                       placeholder="শিক্ষার্থী আইডি লিখুন"
                                       value="<?php echo isset($searchId) ? htmlspecialchars($searchId) : ''; ?>" required>
                            </div>
                            <div class="col-md-12">
                                <button type="submit" name="search_student" class="btn btn-primary">
                                    <i class="fas fa-search me-2"></i>শিক্ষার্থী খুঁজুন
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <?php if ($studentInfo): ?>
                <!-- Debug Information -->
                <div class="alert alert-info">
                    <strong>Debug Info:</strong><br>
                    Search ID: <?php echo isset($searchId) ? $searchId : 'Not set'; ?><br>
                    Student Found: <?php echo $studentInfo ? 'Yes' : 'No'; ?><br>
                    <?php if ($studentInfo): ?>
                        Student Name: <?php echo $studentInfo['first_name'] . ' ' . $studentInfo['last_name']; ?><br>
                        Student Department ID: <?php echo $studentInfo['department_id']; ?><br>
                        Department Name: <?php echo $studentInfo['department_name'] ?? 'Unknown'; ?><br>
                        Required Subjects Count: <?php echo isset($requiredSubjects) ? (is_object($requiredSubjects) && isset($requiredSubjects->data) ? count($requiredSubjects->data) : $requiredSubjects->num_rows) : 0; ?><br>
                        Optional Subjects Count: <?php echo isset($optionalSubjects) ? (is_object($optionalSubjects) && isset($optionalSubjects->data) ? count($optionalSubjects->data) : $optionalSubjects->num_rows) : 0; ?><br>
                        Fourth Subjects Count: <?php echo isset($fourthSubjects) ? (is_object($fourthSubjects) && isset($fourthSubjects->data) ? count($fourthSubjects->data) : $fourthSubjects->num_rows) : 0; ?><br>

                        <?php
                        // Show what subjects exist for this department
                        $deptSubjectsQuery = "SELECT COUNT(*) as count FROM subjects WHERE department_id = ?";
                        $stmt = $conn->prepare($deptSubjectsQuery);
                        $stmt->bind_param("i", $studentInfo['department_id']);
                        $stmt->execute();
                        $deptSubjectsCount = $stmt->get_result()->fetch_assoc()['count'];
                        echo "Total Subjects for Department: " . $deptSubjectsCount . "<br>";
                        ?>
                    <?php endif; ?>
                </div>

                <!-- Student Information -->
                <div class="student-info">
                    <div class="row">
                        <div class="col-md-6">
                            <h4><?php echo $studentInfo['first_name'] . ' ' . $studentInfo['last_name']; ?></h4>
                            <p><strong>শিক্ষার্থী আইডি:</strong> <?php echo $studentInfo['student_id']; ?></p>
                            <p><strong>ইমেইল:</strong> <?php echo $studentInfo['email']; ?></p>
                            <p><strong>ফোন:</strong> <?php echo $studentInfo['phone']; ?></p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>বিভাগ:</strong> <?php echo $studentInfo['department_name']; ?></p>
                            <p><strong>ক্লাস:</strong> <?php echo $studentInfo['class_name']; ?></p>
                            <p><strong>সেশন:</strong> <?php echo $studentInfo['session_name']; ?></p>
                        </div>
                    </div>
                </div>

                <!-- Subject Selection Form -->
                <form method="POST" action="student_subject_selection.php">
                    <input type="hidden" name="student_id" value="<?php echo $studentInfo['id']; ?>">
                    
                    <!-- Selection Counter -->
                    <div class="selection-summary mb-4">
                        <div class="row align-items-center">
                            <div class="col">
                                <h4 class="mb-0">নির্বাচিত বিষয়সমূহ: <span id="selectedCount">0</span>/7</h4>
                            </div>
                            <div class="col-auto">
                                <div class="d-flex gap-2">
                                    <span class="badge bg-primary counter-badge">আবশ্যিক: <span id="requiredCount">0</span></span>
                                    <span class="badge bg-info counter-badge">ঐচ্ছিক: <span id="optionalCount">0</span></span>
                                    <span class="badge bg-warning counter-badge">৪র্থ বিষয়: <span id="fourthCount">0</span>/1</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row mt-3">
                            <div class="col-md-4">
                                <label for="session_id" class="form-label">সেশন নির্বাচন করুন</label>
                                <select name="session_id" id="session_id" class="form-select">
                                    <?php if ($sessions && $sessions->num_rows > 0): ?>
                                        <?php while ($session = $sessions->fetch_assoc()): ?>
                                            <option value="<?php echo $session['id']; ?>" <?php echo ($session['id'] == $currentSession['id']) ? 'selected' : ''; ?>>
                                                <?php echo $session['session_name']; ?>
                                            </option>
                                        <?php endwhile; ?>
                                    <?php endif; ?>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- Required Subjects -->
                    <div class="mb-4">
                        <h4 class="mb-3">আবশ্যিক বিষয়সমূহ</h4>
                        <div class="row">
                            <?php
                            $hasRequiredSubjects = false;
                            if (isset($requiredSubjects)) {
                                if (is_object($requiredSubjects) && isset($requiredSubjects->data)) {
                                    // Array data from categories
                                    $hasRequiredSubjects = count($requiredSubjects->data) > 0;
                                    $subjectsToShow = $requiredSubjects->data;
                                } else {
                                    // MySQL result object
                                    $hasRequiredSubjects = $requiredSubjects->num_rows > 0;
                                    $subjectsToShow = [];
                                    if ($hasRequiredSubjects) {
                                        $requiredSubjects->data_seek(0);
                                        while ($subject = $requiredSubjects->fetch_assoc()) {
                                            $subjectsToShow[] = $subject;
                                        }
                                    }
                                }
                            }

                            if ($hasRequiredSubjects): ?>
                                <?php foreach ($subjectsToShow as $subject): ?>
                                    <div class="col-md-4">
                                        <div class="card subject-card <?php echo in_array($subject['id'], $selectedRequiredIds) ? 'selected' : ''; ?>">
                                            <div class="card-body">
                                                <div class="form-check">
                                                    <input class="form-check-input subject-checkbox required-subject"
                                                           type="checkbox"
                                                           name="required_subjects[]"
                                                           value="<?php echo $subject['id']; ?>"
                                                           id="subject<?php echo $subject['id']; ?>"
                                                           data-category="required"
                                                           <?php echo in_array($subject['id'], $selectedRequiredIds) ? 'checked' : ''; ?>>
                                                    <label class="form-check-label" for="subject<?php echo $subject['id']; ?>">
                                                        <strong><?php echo $subject['subject_name']; ?></strong>
                                                        <br>
                                                        <small class="text-muted"><?php echo $subject['subject_code'] ?? ''; ?></small>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <div class="col-12">
                                    <div class="alert alert-info">কোন আবশ্যিক বিষয় পাওয়া যায়নি</div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Optional Subjects -->
                    <div class="mb-4">
                        <h4 class="mb-3">ঐচ্ছিক বিষয়সমূহ</h4>
                        <div class="row">
                            <?php
                            $hasOptionalSubjects = false;
                            if (isset($optionalSubjects)) {
                                if (is_object($optionalSubjects) && isset($optionalSubjects->data)) {
                                    // Array data from categories
                                    $hasOptionalSubjects = count($optionalSubjects->data) > 0;
                                    $subjectsToShow = $optionalSubjects->data;
                                } else {
                                    // MySQL result object
                                    $hasOptionalSubjects = $optionalSubjects->num_rows > 0;
                                    $subjectsToShow = [];
                                    if ($hasOptionalSubjects) {
                                        $optionalSubjects->data_seek(0);
                                        while ($subject = $optionalSubjects->fetch_assoc()) {
                                            $subjectsToShow[] = $subject;
                                        }
                                    }
                                }
                            }

                            if ($hasOptionalSubjects): ?>
                                <?php foreach ($subjectsToShow as $subject): ?>
                                    <div class="col-md-4">
                                        <div class="card subject-card <?php echo in_array($subject['id'], $selectedOptionalIds) ? 'selected' : ''; ?>">
                                            <div class="card-body">
                                                <div class="form-check">
                                                    <input class="form-check-input subject-checkbox optional-subject"
                                                           type="checkbox"
                                                           name="optional_subjects[]"
                                                           value="<?php echo $subject['id']; ?>"
                                                           id="subject<?php echo $subject['id']; ?>"
                                                           data-category="optional"
                                                           <?php echo in_array($subject['id'], $selectedOptionalIds) ? 'checked' : ''; ?>>
                                                    <label class="form-check-label" for="subject<?php echo $subject['id']; ?>">
                                                        <strong><?php echo $subject['subject_name']; ?></strong>
                                                        <br>
                                                        <small class="text-muted"><?php echo $subject['subject_code'] ?? ''; ?></small>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <div class="col-12">
                                    <div class="alert alert-info">কোন ঐচ্ছিক বিষয় পাওয়া যায়নি</div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Fourth Subjects -->
                    <div class="mb-4">
                        <h4 class="mb-3">৪র্থ বিষয় (সর্বোচ্চ ১টি)</h4>
                        <div class="row">
                            <?php
                            $hasFourthSubjects = false;
                            if (isset($fourthSubjects)) {
                                if (is_object($fourthSubjects) && isset($fourthSubjects->data)) {
                                    // Array data from categories
                                    $hasFourthSubjects = count($fourthSubjects->data) > 0;
                                    $subjectsToShow = $fourthSubjects->data;
                                } else {
                                    // MySQL result object
                                    $hasFourthSubjects = $fourthSubjects->num_rows > 0;
                                    $subjectsToShow = [];
                                    if ($hasFourthSubjects) {
                                        $fourthSubjects->data_seek(0);
                                        while ($subject = $fourthSubjects->fetch_assoc()) {
                                            $subjectsToShow[] = $subject;
                                        }
                                    }
                                }
                            }

                            if ($hasFourthSubjects): ?>
                                <?php foreach ($subjectsToShow as $subject): ?>
                                    <div class="col-md-4">
                                        <div class="card subject-card <?php echo in_array($subject['id'], $selectedFourthIds) ? 'selected' : ''; ?>">
                                            <div class="card-body">
                                                <div class="form-check">
                                                    <input class="form-check-input subject-checkbox fourth-subject"
                                                           type="checkbox"
                                                           name="fourth_subjects[]"
                                                           value="<?php echo $subject['id']; ?>"
                                                           id="subject<?php echo $subject['id']; ?>"
                                                           data-category="fourth"
                                                           <?php echo in_array($subject['id'], $selectedFourthIds) ? 'checked' : ''; ?>>
                                                    <label class="form-check-label" for="subject<?php echo $subject['id']; ?>">
                                                        <strong><?php echo $subject['subject_name']; ?></strong>
                                                        <br>
                                                        <small class="text-muted"><?php echo $subject['subject_code'] ?? ''; ?></small>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <div class="col-12">
                                    <div class="alert alert-info">কোন ৪র্থ বিষয় পাওয়া যায়নি</div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div class="d-grid gap-2 col-md-6 mx-auto mt-4 mb-5">
                        <button type="submit" name="save_selection" class="btn btn-primary btn-lg">
                            <i class="fas fa-save me-2"></i>বিষয় নির্বাচন সংরক্ষণ করুন
                        </button>
                    </div>
                </form>

                <?php endif; ?>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const subjectCheckboxes = document.querySelectorAll('.subject-checkbox');
            const requiredCountElement = document.getElementById('requiredCount');
            const optionalCountElement = document.getElementById('optionalCount');
            const fourthCountElement = document.getElementById('fourthCount');
            const selectedCountElement = document.getElementById('selectedCount');
            
            // Initial count update
            updateCounts();
            
            // Update card styles for selected subjects
            subjectCheckboxes.forEach(checkbox => {
                updateCardStyle(checkbox);
                
                checkbox.addEventListener('change', function() {
                    updateCardStyle(this);
                    updateCounts();
                    
                    // Fourth subject validation - allow only one
                    if (this.dataset.category === 'fourth' && this.checked) {
                        const fourthCheckboxes = document.querySelectorAll('.fourth-subject:checked');
                        if (fourthCheckboxes.length > 1) {
                            fourthCheckboxes.forEach(cb => {
                                if (cb !== this) {
                                    cb.checked = false;
                                    updateCardStyle(cb);
                                }
                            });
                        }
                    }
                });
            });
            
            function updateCardStyle(checkbox) {
                const card = checkbox.closest('.subject-card');
                if (checkbox.checked) {
                    card.classList.add('selected');
                } else {
                    card.classList.remove('selected');
                }
            }
            
            function updateCounts() {
                const requiredSelected = document.querySelectorAll('.required-subject:checked').length;
                const optionalSelected = document.querySelectorAll('.optional-subject:checked').length;
                const fourthSelected = document.querySelectorAll('.fourth-subject:checked').length;
                const totalSelected = requiredSelected + optionalSelected + fourthSelected;
                
                requiredCountElement.textContent = requiredSelected;
                optionalCountElement.textContent = optionalSelected;
                fourthCountElement.textContent = fourthSelected;
                selectedCountElement.textContent = totalSelected;
            }
        });
    </script>
</body>
</html> 