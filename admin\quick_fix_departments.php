<?php
require_once '../includes/dbh.inc.php';

echo "<h3>Quick Fix: Assign Departments to Subjects</h3>";

// First, let's see what we have
echo "<h4>Current Subjects:</h4>";
$subjects = $conn->query("SELECT * FROM subjects");
while ($subject = $subjects->fetch_assoc()) {
    echo "- " . htmlspecialchars($subject['subject_name']) . " (ID: " . $subject['id'] . ")<br>";
}

echo "<h4>Current Departments:</h4>";
$departments = $conn->query("SELECT * FROM departments");
$deptList = [];
while ($dept = $departments->fetch_assoc()) {
    $deptList[] = $dept;
    echo "- " . htmlspecialchars($dept['department_name']) . " (ID: " . $dept['id'] . ")<br>";
}

// Quick assignment based on subject names
echo "<h4>Auto-assigning subjects to departments...</h4>";

// Reset subjects query
$subjects = $conn->query("SELECT * FROM subjects");

while ($subject = $subjects->fetch_assoc()) {
    $subjectName = strtoupper($subject['subject_name']);
    $assignedDept = null;
    
    // Science subjects
    if (strpos($subjectName, 'BIOLOGY') !== false || 
        strpos($subjectName, 'CHEMISTRY') !== false || 
        strpos($subjectName, 'PHYSICS') !== false || 
        strpos($subjectName, 'MATH') !== false ||
        strpos($subjectName, 'SCIENCE') !== false) {
        
        // Find science department
        foreach ($deptList as $dept) {
            if (strpos(strtoupper($dept['department_name']), 'বিজ্ঞান') !== false || 
                strpos(strtoupper($dept['department_name']), 'SCIENCE') !== false) {
                $assignedDept = $dept['id'];
                break;
            }
        }
    }
    
    // Arabic/Islamic subjects
    elseif (strpos($subjectName, 'BOM') !== false || 
            strpos($subjectName, 'ARABIC') !== false || 
            strpos($subjectName, 'QURAN') !== false || 
            strpos($subjectName, 'HADITH') !== false || 
            strpos($subjectName, 'FIQH') !== false ||
            strpos($subjectName, 'ISLAM') !== false) {
        
        // Find Arabic department
        foreach ($deptList as $dept) {
            if (strpos(strtoupper($dept['department_name']), 'আরবি') !== false || 
                strpos(strtoupper($dept['department_name']), 'ARABIC') !== false ||
                strpos(strtoupper($dept['department_name']), 'ইসলাম') !== false) {
                $assignedDept = $dept['id'];
                break;
            }
        }
    }
    
    // General subjects
    else {
        // Find general department
        foreach ($deptList as $dept) {
            if (strpos(strtoupper($dept['department_name']), 'সাধারণ') !== false || 
                strpos(strtoupper($dept['department_name']), 'GENERAL') !== false) {
                $assignedDept = $dept['id'];
                break;
            }
        }
    }
    
    // If no specific department found, assign to first department
    if (!$assignedDept && !empty($deptList)) {
        $assignedDept = $deptList[0]['id'];
    }
    
    // Update the subject
    if ($assignedDept) {
        $updateQuery = "UPDATE subjects SET department_id = ? WHERE id = ?";
        $stmt = $conn->prepare($updateQuery);
        $stmt->bind_param("ii", $assignedDept, $subject['id']);
        
        if ($stmt->execute()) {
            $deptName = '';
            foreach ($deptList as $dept) {
                if ($dept['id'] == $assignedDept) {
                    $deptName = $dept['department_name'];
                    break;
                }
            }
            echo "✅ " . htmlspecialchars($subject['subject_name']) . " → " . htmlspecialchars($deptName) . "<br>";
        } else {
            echo "❌ Failed to assign " . htmlspecialchars($subject['subject_name']) . "<br>";
        }
    }
}

echo "<h4>Verification:</h4>";
$verification = $conn->query("SELECT s.subject_name, d.department_name 
                             FROM subjects s 
                             LEFT JOIN departments d ON s.department_id = d.id 
                             ORDER BY d.department_name, s.subject_name");

$currentDept = '';
while ($row = $verification->fetch_assoc()) {
    if ($currentDept != $row['department_name']) {
        $currentDept = $row['department_name'];
        echo "<br><strong>" . htmlspecialchars($currentDept ?: 'No Department') . ":</strong><br>";
    }
    echo "- " . htmlspecialchars($row['subject_name']) . "<br>";
}

echo "<br><br><a href='subject_categories.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Test Subject Categories Now</a>";
?>
