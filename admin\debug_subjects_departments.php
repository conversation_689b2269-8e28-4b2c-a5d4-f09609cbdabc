<?php
require_once '../includes/dbh.inc.php';

echo "<h3>Debug: Subjects and Departments Mapping</h3>";

// Check subjects table structure
echo "<h4>1. Subjects Table Structure:</h4>";
$result = $conn->query("DESCRIBE subjects");
if ($result) {
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['Field'] . "</td>";
        echo "<td>" . $row['Type'] . "</td>";
        echo "<td>" . $row['Null'] . "</td>";
        echo "<td>" . $row['Key'] . "</td>";
        echo "<td>" . $row['Default'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
}

// Check if subject_departments table exists
echo "<h4>2. Subject_Departments Table:</h4>";
$checkTable = $conn->query("SHOW TABLES LIKE 'subject_departments'");
if ($checkTable && $checkTable->num_rows > 0) {
    echo "✅ subject_departments table exists<br>";
    
    $result2 = $conn->query("SELECT COUNT(*) as count FROM subject_departments");
    $count = $result2->fetch_assoc();
    echo "Records in subject_departments: " . $count['count'] . "<br>";
    
    if ($count['count'] > 0) {
        echo "<h5>Sample data:</h5>";
        $sample = $conn->query("SELECT sd.*, s.subject_name, d.department_name 
                               FROM subject_departments sd 
                               LEFT JOIN subjects s ON sd.subject_id = s.id 
                               LEFT JOIN departments d ON sd.department_id = d.id 
                               LIMIT 10");
        if ($sample && $sample->num_rows > 0) {
            echo "<table border='1' style='border-collapse: collapse;'>";
            echo "<tr><th>Subject ID</th><th>Subject Name</th><th>Department ID</th><th>Department Name</th></tr>";
            while ($row = $sample->fetch_assoc()) {
                echo "<tr>";
                echo "<td>" . $row['subject_id'] . "</td>";
                echo "<td>" . htmlspecialchars($row['subject_name']) . "</td>";
                echo "<td>" . $row['department_id'] . "</td>";
                echo "<td>" . htmlspecialchars($row['department_name']) . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
    }
} else {
    echo "❌ subject_departments table does not exist<br>";
}

// Check subjects with department_id
echo "<h4>3. Subjects with Direct Department ID:</h4>";
$directDept = $conn->query("SELECT s.id, s.subject_name, s.department_id, d.department_name 
                           FROM subjects s 
                           LEFT JOIN departments d ON s.department_id = d.id 
                           WHERE s.department_id IS NOT NULL 
                           LIMIT 10");
if ($directDept && $directDept->num_rows > 0) {
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Subject ID</th><th>Subject Name</th><th>Department ID</th><th>Department Name</th></tr>";
    while ($row = $directDept->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['id'] . "</td>";
        echo "<td>" . htmlspecialchars($row['subject_name']) . "</td>";
        echo "<td>" . $row['department_id'] . "</td>";
        echo "<td>" . htmlspecialchars($row['department_name']) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "No subjects with direct department_id found<br>";
}

// Check all subjects
echo "<h4>4. All Subjects:</h4>";
$allSubjects = $conn->query("SELECT * FROM subjects LIMIT 10");
if ($allSubjects && $allSubjects->num_rows > 0) {
    echo "<table border='1' style='border-collapse: collapse;'>";
    $first = true;
    while ($row = $allSubjects->fetch_assoc()) {
        if ($first) {
            echo "<tr>";
            foreach (array_keys($row) as $key) {
                echo "<th>$key</th>";
            }
            echo "</tr>";
            $first = false;
        }
        echo "<tr>";
        foreach ($row as $value) {
            echo "<td>" . htmlspecialchars($value) . "</td>";
        }
        echo "</tr>";
    }
    echo "</table>";
}

// Check departments
echo "<h4>5. All Departments:</h4>";
$allDepts = $conn->query("SELECT * FROM departments");
if ($allDepts && $allDepts->num_rows > 0) {
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>ID</th><th>Department Name</th></tr>";
    while ($row = $allDepts->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['id'] . "</td>";
        echo "<td>" . htmlspecialchars($row['department_name']) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
}

echo "<br><a href='subject_categories.php'>Back to Subject Categories</a>";
?>
