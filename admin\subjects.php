<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Create subjects table if it doesn't exist
$subjectsTableQuery = "CREATE TABLE IF NOT EXISTS subjects (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    subject_name VARCHAR(100) NOT NULL,
    subject_code VARCHAR(20) NOT NULL,
    department_id INT(11) NULL,
    category VARCHAR(255) DEFAULT 'required',
    description TEXT,
    is_active TINYINT(1) DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE CASCADE
)";
$conn->query($subjectsTableQuery);

// Check if unique constraints exist and remove them
$checkUniqueConstraintsQuery = "SHOW CREATE TABLE subjects";
$uniqueConstraintsResult = $conn->query($checkUniqueConstraintsQuery);
if ($uniqueConstraintsResult && $uniqueConstraintsResult->num_rows > 0) {
    $tableCreateQuery = $uniqueConstraintsResult->fetch_assoc()['Create Table'];
    
    // Check for unique constraint on subject_name
    if (strpos($tableCreateQuery, 'UNIQUE KEY `subject_name`') !== false) {
        $dropSubjectNameUniqueQuery = "ALTER TABLE subjects DROP INDEX `subject_name`";
        $conn->query($dropSubjectNameUniqueQuery);
    }
    
    // Check for unique constraint on subject_code
    if (strpos($tableCreateQuery, 'UNIQUE KEY `subject_code`') !== false) {
        $dropSubjectCodeUniqueQuery = "ALTER TABLE subjects DROP INDEX `subject_code`";
        $conn->query($dropSubjectCodeUniqueQuery);
    }
}

// Create a mapping table for subjects to departments (many-to-many)
$subjectDeptTableQuery = "CREATE TABLE IF NOT EXISTS subject_departments (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    subject_id INT(11) NOT NULL,
    department_id INT(11) NOT NULL,
    FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE,
    FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE CASCADE,
    UNIQUE KEY (subject_id, department_id)
)";
$conn->query($subjectDeptTableQuery);

// Check if category column exists and modify it to VARCHAR if it's ENUM
$checkCategoryColumnQuery = "SHOW COLUMNS FROM subjects LIKE 'category'";
$categoryColumnResult = $conn->query($checkCategoryColumnQuery);
if ($categoryColumnResult->num_rows > 0) {
    $categoryColumn = $categoryColumnResult->fetch_assoc();
    if (strpos($categoryColumn['Type'], 'enum') !== false) {
        $alterCategoryColumnQuery = "ALTER TABLE subjects MODIFY COLUMN category VARCHAR(255) DEFAULT 'required'";
        $conn->query($alterCategoryColumnQuery);
    }
}

// Check if is_active column exists and add it if necessary
$checkIsActiveColumnQuery = "SHOW COLUMNS FROM subjects LIKE 'is_active'";
$isActiveColumnResult = $conn->query($checkIsActiveColumnQuery);
if ($isActiveColumnResult->num_rows == 0) {
    $addIsActiveColumnQuery = "ALTER TABLE subjects ADD COLUMN is_active TINYINT(1) DEFAULT 1";
    $conn->query($addIsActiveColumnQuery);
}

// Check if credits column exists and remove it if necessary
$checkCreditsColumnQuery = "SHOW COLUMNS FROM subjects LIKE 'credits'";
$creditsColumnResult = $conn->query($checkCreditsColumnQuery);
if ($creditsColumnResult->num_rows > 0) {
    $dropCreditsColumnQuery = "ALTER TABLE subjects DROP COLUMN credits";
    $conn->query($dropCreditsColumnQuery);
}

// Check if department_id column exists before trying to modify it
$checkDeptIdColumnQuery = "SHOW COLUMNS FROM subjects LIKE 'department_id'";
$deptIdColumnResult = $conn->query($checkDeptIdColumnQuery);
if ($deptIdColumnResult && $deptIdColumnResult->num_rows > 0) {
    // Column exists, update it to allow NULL
    $alterForeignKeyQuery = "ALTER TABLE subjects MODIFY COLUMN department_id INT(11) NULL";
    $conn->query($alterForeignKeyQuery);
}

// Handle subject addition
if (isset($_POST['add_subject'])) {
    $subject_name = $_POST['subject_name'];
    $subject_code = $_POST['subject_code'];
    $department_ids = isset($_POST['department_id']) ? $_POST['department_id'] : [];
    $categories = isset($_POST['category']) ? $_POST['category'] : ['required'];
    $description = $_POST['description'];
    $is_active = isset($_POST['is_active']) ? 1 : 0;
    
    // Convert categories array to string
    $category = is_array($categories) ? implode(',', $categories) : $categories;
    
    // Validate input
    if (empty($subject_name) || empty($subject_code)) {
        $errorMessage = "বিষয়ের নাম এবং কোড অবশ্যই পূরণ করতে হবে!";
    } else {
        $hasError = false;
        
        // Begin transaction
        $conn->begin_transaction();
        
        try {
            // Check if department_id column exists
            $checkDeptIdColumnQuery = "SHOW COLUMNS FROM subjects LIKE 'department_id'";
            $deptIdColumnResult = $conn->query($checkDeptIdColumnQuery);

            // Insert the new subject with or without department_id based on column existence
            if ($deptIdColumnResult && $deptIdColumnResult->num_rows > 0) {
                // Column exists, include it in the insert
                $insertQuery = "INSERT INTO subjects (subject_name, subject_code, department_id, category, description, is_active)
                               VALUES (?, ?, NULL, ?, ?, ?)";
                $stmt = $conn->prepare($insertQuery);
                $stmt->bind_param("ssssi", $subject_name, $subject_code, $category, $description, $is_active);
            } else {
                // Column doesn't exist, exclude it from the insert
                $insertQuery = "INSERT INTO subjects (subject_name, subject_code, category, description, is_active)
                               VALUES (?, ?, ?, ?, ?)";
                $stmt = $conn->prepare($insertQuery);
                $stmt->bind_param("ssssi", $subject_name, $subject_code, $category, $description, $is_active);
            }

            $stmt->execute();
            $subject_id = $conn->insert_id;
            
            // Insert department mappings
            if (!empty($department_ids)) {
                foreach ($department_ids as $dept_id) {
                    if (!empty($dept_id)) {
                        // Check if this subject with the same code already exists for this department
                        $checkDeptSubjectQuery = "SELECT COUNT(*) as count FROM subjects s 
                                                 JOIN subject_departments sd ON s.id = sd.subject_id 
                                                 WHERE s.subject_code = ? AND sd.department_id = ?";
                        $checkStmt = $conn->prepare($checkDeptSubjectQuery);
                        $checkStmt->bind_param("si", $subject_code, $dept_id);
                        $checkStmt->execute();
                        $checkResult = $checkStmt->get_result();
                        $count = $checkResult->fetch_assoc()['count'];
                        
                        if ($count > 0) {
                            $hasError = true;
                            $errorMessage = "বিষয় কোড '$subject_code' ইতিমধ্যে এই বিভাগে বিদ্যমান!";
                            break;
                        }
                        
                        $insertDeptQuery = "INSERT INTO subject_departments (subject_id, department_id) VALUES (?, ?)";
                        $deptStmt = $conn->prepare($insertDeptQuery);
                        $deptStmt->bind_param("ii", $subject_id, $dept_id);
                        $deptStmt->execute();
                        $deptStmt->close();
                    }
                }
            }
            
            if ($hasError) {
                // Rollback transaction on error
                $conn->rollback();
            } else {
                // Commit transaction
                $conn->commit();
                $successMessage = "বিষয় সফলভাবে যোগ করা হয়েছে!";
            }

            // Close the statement
            if (isset($stmt) && $stmt) {
                $stmt->close();
            }
        } catch (Exception $e) {
            // Rollback transaction on error
            $conn->rollback();
            $errorMessage = "বিষয় যোগ করতে সমস্যা হয়েছে: " . $e->getMessage();

            // Close the statement if it exists
            if (isset($stmt) && $stmt) {
                $stmt->close();
            }
        }
    }
}

// Handle subject update
if (isset($_POST['update_subject'])) {
    $subject_id = $_POST['subject_id'];
    $subject_name = $_POST['subject_name'];
    $subject_code = $_POST['subject_code'];
    $department_ids = isset($_POST['department_id']) ? $_POST['department_id'] : [];
    $categories = isset($_POST['category']) ? $_POST['category'] : ['required'];
    $description = $_POST['description'];
    $is_active = isset($_POST['is_active']) ? 1 : 0;
    
    // Convert categories array to string
    $category = is_array($categories) ? implode(',', $categories) : $categories;
    
    // Validate input
    if (empty($subject_name) || empty($subject_code)) {
        $errorMessage = "বিষয়ের নাম এবং কোড অবশ্যই পূরণ করতে হবে!";
    } else {
        $hasError = false;
        
        // Begin transaction
        $conn->begin_transaction();
        
        try {
            // Check if department_id column exists
            $checkDeptIdColumnQuery = "SHOW COLUMNS FROM subjects LIKE 'department_id'";
            $deptIdColumnResult = $conn->query($checkDeptIdColumnQuery);

            // Update the subject with or without department_id based on column existence
            if ($deptIdColumnResult && $deptIdColumnResult->num_rows > 0) {
                // Column exists, include it in the update
                $updateQuery = "UPDATE subjects SET subject_name = ?, subject_code = ?, department_id = NULL,
                              category = ?, description = ?, is_active = ? WHERE id = ?";
                $stmt = $conn->prepare($updateQuery);
                $stmt->bind_param("ssssii", $subject_name, $subject_code, $category, $description, $is_active, $subject_id);
            } else {
                // Column doesn't exist, exclude it from the update
                $updateQuery = "UPDATE subjects SET subject_name = ?, subject_code = ?,
                              category = ?, description = ?, is_active = ? WHERE id = ?";
                $stmt = $conn->prepare($updateQuery);
                $stmt->bind_param("ssssii", $subject_name, $subject_code, $category, $description, $is_active, $subject_id);
            }

            $stmt->execute();
            
            // Delete existing department mappings
            $deleteMapQuery = "DELETE FROM subject_departments WHERE subject_id = ?";
            $deleteStmt = $conn->prepare($deleteMapQuery);
            $deleteStmt->bind_param("i", $subject_id);
            $deleteStmt->execute();
            $deleteStmt->close();
            
            // Insert department mappings
            if (!empty($department_ids)) {
                foreach ($department_ids as $dept_id) {
                    if (!empty($dept_id)) {
                        // Check if this subject with the same code already exists for this department (excluding current subject)
                        $checkDeptSubjectQuery = "SELECT COUNT(*) as count FROM subjects s 
                                                 JOIN subject_departments sd ON s.id = sd.subject_id 
                                                 WHERE s.subject_code = ? AND sd.department_id = ? AND s.id != ?";
                        $checkStmt = $conn->prepare($checkDeptSubjectQuery);
                        $checkStmt->bind_param("sii", $subject_code, $dept_id, $subject_id);
                        $checkStmt->execute();
                        $checkResult = $checkStmt->get_result();
                        $count = $checkResult->fetch_assoc()['count'];
                        
                        if ($count > 0) {
                            $hasError = true;
                            $errorMessage = "বিষয় কোড '$subject_code' ইতিমধ্যে এই বিভাগে বিদ্যমান!";
                            break;
                        }
                        
                        $insertDeptQuery = "INSERT INTO subject_departments (subject_id, department_id) VALUES (?, ?)";
                        $deptStmt = $conn->prepare($insertDeptQuery);
                        $deptStmt->bind_param("ii", $subject_id, $dept_id);
                        $deptStmt->execute();
                        $deptStmt->close();
                    }
                }
            }
            
            if ($hasError) {
                // Rollback transaction on error
                $conn->rollback();
            } else {
                // Commit transaction
                $conn->commit();
                $successMessage = "বিষয় সফলভাবে আপডেট করা হয়েছে!";
            }

            // Close the statement
            if (isset($stmt) && $stmt) {
                $stmt->close();
            }
        } catch (Exception $e) {
            // Rollback transaction on error
            $conn->rollback();
            $errorMessage = "বিষয় আপডেট করতে সমস্যা হয়েছে: " . $e->getMessage();

            // Close the statement if it exists
            if (isset($stmt) && $stmt) {
                $stmt->close();
            }
        }
    }
}

// Handle subject deletion
if (isset($_GET['delete'])) {
    $subject_id = $_GET['delete'];
    $deleteQuery = "DELETE FROM subjects WHERE id = ?";
    $stmt = $conn->prepare($deleteQuery);
    $stmt->bind_param("i", $subject_id);
    
    if ($stmt->execute()) {
        $successMessage = "বিষয় সফলভাবে মুছে ফেলা হয়েছে!";
    } else {
        $errorMessage = "বিষয় মুছতে সমস্যা হয়েছে: " . $conn->error;
    }
    $stmt->close();
}

// Get subject to edit
$editSubject = null;
$editSubjectDepartments = [];
if (isset($_GET['edit'])) {
    $subject_id = $_GET['edit'];
    $editQuery = "SELECT * FROM subjects WHERE id = ?";
    $stmt = $conn->prepare($editQuery);
    $stmt->bind_param("i", $subject_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 1) {
        $editSubject = $result->fetch_assoc();
        
        // Get departments for this subject
        $deptQuery = "SELECT department_id FROM subject_departments WHERE subject_id = ?";
        $deptStmt = $conn->prepare($deptQuery);
        $deptStmt->bind_param("i", $subject_id);
        $deptStmt->execute();
        $deptResult = $deptStmt->get_result();
        
        while ($row = $deptResult->fetch_assoc()) {
            $editSubjectDepartments[] = $row['department_id'];
        }
        $deptStmt->close();
    }
    $stmt->close();
}

// Get subjects list with department names
$subjectsQuery = "SELECT s.*, GROUP_CONCAT(DISTINCT d.department_name SEPARATOR ', ') as department_names
                 FROM subjects s
                 LEFT JOIN subject_departments sd ON s.id = sd.subject_id
                 LEFT JOIN departments d ON sd.department_id = d.id
                 GROUP BY s.id
                 ORDER BY s.subject_name";

// Apply department filter if set
$departmentFilter = isset($_GET['filter_department']) ? $_GET['filter_department'] : '';
$categoryFilter = isset($_GET['filter_category']) ? $_GET['filter_category'] : '';

$whereClause = "";
$havingClause = "";

if (!empty($departmentFilter)) {
    if ($departmentFilter === 'all') {
        $havingClause = " HAVING COUNT(sd.department_id) = 0";
    } else {
        $whereClause = " WHERE sd.department_id = '$departmentFilter'";
    }
}

if (!empty($categoryFilter)) {
    if (empty($whereClause)) {
        $whereClause = " WHERE s.category LIKE '%$categoryFilter%'";
    } else {
        $whereClause .= " AND s.category LIKE '%$categoryFilter%'";
    }
}

if (!empty($whereClause) || !empty($havingClause)) {
    $subjectsQuery = "SELECT s.*, GROUP_CONCAT(DISTINCT d.department_name SEPARATOR ', ') as department_names
                     FROM subjects s
                     LEFT JOIN subject_departments sd ON s.id = sd.subject_id
                     LEFT JOIN departments d ON sd.department_id = d.id
                     $whereClause
                     GROUP BY s.id
                     $havingClause
                     ORDER BY s.subject_name";
}

$subjects = $conn->query($subjectsQuery);

// Get departments for dropdown
$departmentsQuery = "SELECT * FROM departments ORDER BY department_name";
$departments = $conn->query($departmentsQuery);
// Keep a copy for the filter dropdown
$departmentsForFilter = $conn->query($departmentsQuery);

// Fix the in_array check for categories
if (isset($editSubject)) {
    // Convert category string to array
    $editSubjectCategories = explode(',', $editSubject['category']);
} else {
    $editSubjectCategories = ['required'];
}

// Fix the in_array check for is_active
$isActiveChecked = (!isset($editSubject) || $editSubject['is_active'] == 1) ? 'checked' : '';
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>বিষয় ব্যবস্থাপনা - কলেজ ম্যানেজমেন্ট সিস্টেম</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar">
                <div class="text-center mb-4">
                    <h3>অ্যাডমিন প্যানেল</h3>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i> ড্যাশবোর্ড
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="students.php">
                            <i class="fas fa-user-graduate me-2"></i> শিক্ষার্থী
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="teachers.php">
                            <i class="fas fa-chalkboard-teacher me-2"></i> শিক্ষক
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="staff.php">
                            <i class="fas fa-user-tie me-2"></i> কর্মচারী
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="courses.php">
                            <i class="fas fa-book me-2"></i> কোর্স
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="subjects.php">
                            <i class="fas fa-book-open me-2"></i> বিষয়
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="subject_categories.php">
                            <i class="fas fa-tags me-2"></i> বিষয় ক্যাটাগরি
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="classes.php">
                            <i class="fas fa-chalkboard me-2"></i> ক্লাস
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="sessions.php">
                            <i class="fas fa-calendar-alt me-2"></i> সেশন
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="exams.php">
                            <i class="fas fa-file-alt me-2"></i> পরীক্ষা
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="results.php">
                            <i class="fas fa-chart-bar me-2"></i> ফলাফল
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="fees.php">
                            <i class="fas fa-money-bill-wave me-2"></i> ফি
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reports.php">
                            <i class="fas fa-file-pdf me-2"></i> রিপোর্ট
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../includes/logout.inc.php">
                            <i class="fas fa-sign-out-alt me-2"></i> লগআউট
                        </a>
                    </li>
                </ul>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <div class="row mb-4">
                    <div class="col">
                        <h2>বিষয় ব্যবস্থাপনা</h2>
                        <p class="text-muted">বিষয়সমূহ যোগ করুন, সম্পাদনা করুন এবং পরিচালনা করুন</p>
                    </div>
                </div>

                <?php if (isset($errorMessage)): ?>
                    <div class="alert alert-danger" role="alert">
                        <?php echo $errorMessage; ?>
                    </div>
                <?php endif; ?>

                <?php if (isset($successMessage)): ?>
                    <div class="alert alert-success" role="alert">
                        <?php echo $successMessage; ?>
                    </div>
                <?php endif; ?>

                <div class="row">
                    <!-- Add/Edit Subject Form -->
                    <div class="col-md-4 mb-4">
                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                <h5 class="card-title mb-0">
                                    <?php echo isset($editSubject) ? 'বিষয় সম্পাদনা করুন' : 'নতুন বিষয় যোগ করুন'; ?>
                                </h5>
                            </div>
                            <div class="card-body">
                                <form method="POST" action="subjects.php<?php echo isset($editSubject) ? '?edit=' . $editSubject['id'] : ''; ?>">
                                    <?php if (isset($editSubject)): ?>
                                        <input type="hidden" name="subject_id" value="<?php echo $editSubject['id']; ?>">
                                    <?php endif; ?>
                                    
                                    <div class="mb-3">
                                        <label for="subject_name" class="form-label">বিষয়ের নাম*</label>
                                        <input type="text" class="form-control" id="subject_name" name="subject_name" 
                                               value="<?php echo isset($editSubject) ? $editSubject['subject_name'] : ''; ?>" required>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="subject_code" class="form-label">বিষয় কোড*</label>
                                        <input type="text" class="form-control" id="subject_code" name="subject_code" 
                                               value="<?php echo isset($editSubject) ? $editSubject['subject_code'] : ''; ?>" required>
                                        <small class="form-text text-muted">উদাহরণ: CSE101, PHY102, ENG201</small>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="department_id" class="form-label">বিভাগ</label>
                                        <select class="form-select" id="department_id" name="department_id[]" multiple>
                                            <option value="">সকল বিভাগ</option>
                                            <?php if ($departments && $departments->num_rows > 0): ?>
                                                <?php while ($dept = $departments->fetch_assoc()): ?>
                                                    <option value="<?php echo $dept['id']; ?>" <?php echo (isset($editSubject) && in_array($dept['id'], $editSubjectDepartments)) ? 'selected' : ''; ?>>
                                                        <?php echo $dept['department_name']; ?>
                                                    </option>
                                                <?php endwhile; ?>
                                            <?php endif; ?>
                                        </select>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="category" class="form-label">বিষয়ের ধরন</label>
                                        <select class="form-select" id="category" name="category[]" required multiple>
                                            <option value="required" <?php echo (!isset($editSubject) || in_array('required', $editSubjectCategories)) ? 'selected' : ''; ?>>আবশ্যিক</option>
                                            <option value="optional" <?php echo (isset($editSubject) && in_array('optional', $editSubjectCategories)) ? 'selected' : ''; ?>>নির্বাচিত</option>
                                            <option value="fourth" <?php echo (isset($editSubject) && in_array('fourth', $editSubjectCategories)) ? 'selected' : ''; ?>>চতুর্থ বিষয়</option>
                                        </select>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="description" class="form-label">বিবরণ</label>
                                        <textarea class="form-control" id="description" name="description" rows="3"><?php echo isset($editSubject) ? $editSubject['description'] : ''; ?></textarea>
                                    </div>
                                    
                                    <div class="mb-3 form-check">
                                        <input type="checkbox" class="form-check-input" id="is_active" name="is_active" <?php echo $isActiveChecked; ?>>
                                        <label class="form-check-label" for="is_active">সক্রিয়</label>
                                    </div>
                                    
                                    <?php if (isset($editSubject)): ?>
                                        <button type="submit" name="update_subject" class="btn btn-primary">আপডেট করুন</button>
                                        <a href="subjects.php" class="btn btn-secondary">বাতিল করুন</a>
                                    <?php else: ?>
                                        <button type="submit" name="add_subject" class="btn btn-primary">যোগ করুন</button>
                                    <?php endif; ?>
                                </form>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Subjects List -->
                    <div class="col-md-8 mb-4">
                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                <h5 class="card-title mb-0">বিষয় তালিকা</h5>
                            </div>
                            <div class="card-body">
                                <!-- Department Filter -->
                                <div class="mb-3">
                                    <form method="GET" action="subjects.php" class="row g-3">
                                        <div class="col-md-5">
                                            <label for="filter_department" class="form-label">বিভাগ অনুযায়ী ফিল্টার</label>
                                            <select name="filter_department" id="filter_department" class="form-select">
                                                <option value="">সকল বিষয় দেখুন</option>
                                                <option value="all" <?php echo ($departmentFilter === 'all') ? 'selected' : ''; ?>>শুধু সকল বিভাগের বিষয়সমূহ</option>
                                                <?php if ($departmentsForFilter && $departmentsForFilter->num_rows > 0): ?>
                                                    <?php while ($dept = $departmentsForFilter->fetch_assoc()): ?>
                                                        <option value="<?php echo $dept['id']; ?>" <?php echo ($departmentFilter == $dept['id']) ? 'selected' : ''; ?>>
                                                            <?php echo $dept['department_name']; ?>
                                                        </option>
                                                    <?php endwhile; ?>
                                                <?php endif; ?>
                                            </select>
                                        </div>
                                        <div class="col-md-4">
                                            <label for="filter_category" class="form-label">বিষয়ের ধরন অনুযায়ী ফিল্টার</label>
                                            <select name="filter_category" id="filter_category" class="form-select">
                                                <option value="">সকল ধরনের বিষয়</option>
                                                <option value="required" <?php echo ($categoryFilter === 'required') ? 'selected' : ''; ?>>আবশ্যিক</option>
                                                <option value="optional" <?php echo ($categoryFilter === 'optional') ? 'selected' : ''; ?>>নির্বাচিত</option>
                                                <option value="fourth" <?php echo ($categoryFilter === 'fourth') ? 'selected' : ''; ?>>চতুর্থ বিষয়</option>
                                            </select>
                                        </div>
                                        <div class="col-md-3 d-flex align-items-end">
                                            <div>
                                                <button type="submit" class="btn btn-primary">
                                                    <i class="fas fa-filter me-2"></i>ফিল্টার করুন
                                                </button>
                                                <?php if (!empty($departmentFilter) || !empty($categoryFilter)): ?>
                                                    <a href="subjects.php" class="btn btn-secondary ms-2">
                                                        <i class="fas fa-times me-2"></i>রিসেট
                                                    </a>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                                
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>বিষয়ের নাম</th>
                                                <th>কোড</th>
                                                <th>বিভাগ</th>
                                                <th>ধরন</th>
                                                <th>স্ট্যাটাস</th>
                                                <th>অ্যাকশন</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php if ($subjects && $subjects->num_rows > 0): ?>
                                                <?php while ($subject = $subjects->fetch_assoc()): ?>
                                                    <tr>
                                                        <td><?php echo htmlspecialchars($subject['subject_name']); ?></td>
                                                        <td><?php echo htmlspecialchars($subject['subject_code']); ?></td>
                                                        <td><?php echo $subject['department_names'] ? htmlspecialchars($subject['department_names']) : 'সকল বিভাগ'; ?></td>
                                                        <td>
                                                            <?php
                                                            $category_text = '';
                                                            $categories = explode(',', $subject['category']);
                                                            foreach ($categories as $cat) {
                                                                switch($cat) {
                                                                    case 'required':
                                                                        $category_text .= 'আবশ্যিক, ';
                                                                        break;
                                                                    case 'optional':
                                                                        $category_text .= 'নির্বাচিত, ';
                                                                        break;
                                                                    case 'fourth':
                                                                        $category_text .= 'চতুর্থ বিষয়, ';
                                                                        break;
                                                                    default:
                                                                        $category_text .= 'আবশ্যিক, ';
                                                                        break;
                                                                }
                                                            }
                                                            $category_text = rtrim($category_text, ', ');
                                                            echo $category_text;
                                                            ?>
                                                        </td>
                                                        <td>
                                                            <?php if ($subject['is_active']): ?>
                                                                <span class="badge bg-success">সক্রিয়</span>
                                                            <?php else: ?>
                                                                <span class="badge bg-danger">নিষ্ক্রিয়</span>
                                                            <?php endif; ?>
                                                        </td>
                                                        <td>
                                                            <a href="subjects.php?edit=<?php echo $subject['id']; ?>" class="btn btn-sm btn-warning">
                                                                <i class="fas fa-edit"></i>
                                                            </a>
                                                            <a href="subjects.php?delete=<?php echo $subject['id']; ?>" 
                                                               class="btn btn-sm btn-danger" 
                                                               onclick="return confirm('আপনি কি নিশ্চিত যে আপনি এই বিষয়টি মুছে ফেলতে চান?')">
                                                                <i class="fas fa-trash"></i>
                                                            </a>
                                                        </td>
                                                    </tr>
                                                <?php endwhile; ?>
                                            <?php else: ?>
                                                <tr>
                                                    <td colspan="6" class="text-center">কোন বিষয় পাওয়া যায়নি</td>
                                                </tr>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <script>
        $(document).ready(function() {
            // Initialize Select2 for multi-select dropdowns
            $('#department_id').select2({
                placeholder: 'বিভাগ নির্বাচন করুন',
                allowClear: true
            });
            
            $('#category').select2({
                placeholder: 'বিষয়ের ধরন নির্বাচন করুন',
                allowClear: true
            });
            
            // Handle form submission
            $('form').submit(function() {
                // Ensure at least one category is selected
                if ($('#category').val().length === 0) {
                    alert('অন্তত একটি বিষয়ের ধরন নির্বাচন করুন!');
                    return false;
                }
                return true;
            });
        });
    </script>
</body>
</html> 