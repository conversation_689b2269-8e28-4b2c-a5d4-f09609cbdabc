<?php
require_once '../includes/dbh.inc.php';

echo "<h3>Assign Subjects to Departments</h3>";

// Get all departments
$departments = $conn->query("SELECT * FROM departments ORDER BY department_name");
$depts = [];
while ($dept = $departments->fetch_assoc()) {
    $depts[] = $dept;
}

// Get all subjects
$subjects = $conn->query("SELECT * FROM subjects ORDER BY subject_name");

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['assign'])) {
    $subject_id = $_POST['subject_id'];
    $department_id = $_POST['department_id'];
    
    // Check if subjects table has department_id column
    $checkColumn = $conn->query("SHOW COLUMNS FROM subjects LIKE 'department_id'");
    if ($checkColumn->num_rows > 0) {
        // Update subjects table directly
        $updateQuery = "UPDATE subjects SET department_id = ? WHERE id = ?";
        $stmt = $conn->prepare($updateQuery);
        $stmt->bind_param("ii", $department_id, $subject_id);
        if ($stmt->execute()) {
            echo "<div style='color: green;'>✅ Subject assigned to department successfully!</div>";
        } else {
            echo "<div style='color: red;'>❌ Error: " . $conn->error . "</div>";
        }
    } else {
        // Use subject_departments table
        $insertQuery = "INSERT INTO subject_departments (subject_id, department_id) VALUES (?, ?) 
                       ON DUPLICATE KEY UPDATE department_id = VALUES(department_id)";
        $stmt = $conn->prepare($insertQuery);
        $stmt->bind_param("ii", $subject_id, $department_id);
        if ($stmt->execute()) {
            echo "<div style='color: green;'>✅ Subject assigned to department successfully!</div>";
        } else {
            echo "<div style='color: red;'>❌ Error: " . $conn->error . "</div>";
        }
    }
}

// Auto-assign some common subjects
if (isset($_POST['auto_assign'])) {
    echo "<h4>Auto-assigning subjects...</h4>";
    
    // Get department IDs
    $deptMap = [];
    foreach ($depts as $dept) {
        $deptMap[strtolower($dept['department_name'])] = $dept['id'];
    }
    
    // Common subject-department mappings
    $autoAssignments = [
        'BIOLOGY' => 'বিজ্ঞান',
        'CHEMISTRY' => 'বিজ্ঞান', 
        'PHYSICS' => 'বিজ্ঞান',
        'MATH' => 'বিজ্ঞান',
        'MATHEMATICS' => 'বিজ্ঞান',
        'BOM' => 'আরবি',
        'ARABIC' => 'আরবি',
        'QURAN' => 'আরবি',
        'HADITH' => 'আরবি',
        'FIQH' => 'আরবি',
        'ENGLISH' => 'সাধারণ',
        'BANGLA' => 'সাধারণ',
        'HISTORY' => 'সাধারণ',
        'GEOGRAPHY' => 'সাধারণ'
    ];
    
    $subjects->data_seek(0);
    while ($subject = $subjects->fetch_assoc()) {
        $subjectName = strtoupper($subject['subject_name']);
        
        foreach ($autoAssignments as $keyword => $deptName) {
            if (strpos($subjectName, $keyword) !== false) {
                $deptNameLower = strtolower($deptName);
                if (isset($deptMap[$deptNameLower])) {
                    $dept_id = $deptMap[$deptNameLower];
                    
                    // Update subject
                    $updateQuery = "UPDATE subjects SET department_id = ? WHERE id = ?";
                    $stmt = $conn->prepare($updateQuery);
                    $stmt->bind_param("ii", $dept_id, $subject['id']);
                    if ($stmt->execute()) {
                        echo "✅ " . htmlspecialchars($subject['subject_name']) . " → " . htmlspecialchars($deptName) . "<br>";
                    }
                }
                break;
            }
        }
    }
}
?>

<form method="POST" style="margin: 20px 0;">
    <button type="submit" name="auto_assign" style="background: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 5px;">
        🚀 Auto-Assign Common Subjects
    </button>
</form>

<h4>Manual Assignment:</h4>
<form method="POST">
    <table border="1" style="border-collapse: collapse; width: 100%;">
        <tr>
            <th>Subject</th>
            <th>Current Department</th>
            <th>Assign to Department</th>
            <th>Action</th>
        </tr>
        <?php 
        $subjects->data_seek(0);
        while ($subject = $subjects->fetch_assoc()): 
            // Get current department
            $currentDept = '';
            if (!empty($subject['department_id'])) {
                $deptQuery = $conn->query("SELECT department_name FROM departments WHERE id = " . $subject['department_id']);
                if ($deptQuery && $deptQuery->num_rows > 0) {
                    $currentDept = $deptQuery->fetch_assoc()['department_name'];
                }
            }
        ?>
        <tr>
            <td><?php echo htmlspecialchars($subject['subject_name']); ?></td>
            <td><?php echo htmlspecialchars($currentDept); ?></td>
            <td>
                <select name="department_id">
                    <option value="">Select Department</option>
                    <?php foreach ($depts as $dept): ?>
                        <option value="<?php echo $dept['id']; ?>" <?php echo ($dept['id'] == $subject['department_id']) ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($dept['department_name']); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </td>
            <td>
                <button type="submit" name="assign" onclick="document.querySelector('input[name=subject_id]').value='<?php echo $subject['id']; ?>'" 
                        style="background: #007bff; color: white; padding: 5px 10px; border: none; border-radius: 3px;">
                    Assign
                </button>
            </td>
        </tr>
        <?php endwhile; ?>
    </table>
    <input type="hidden" name="subject_id" value="">
</form>

<br><a href="subject_categories.php">Back to Subject Categories</a>
