<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Create departments table if it doesn't exist
$departmentsTableQuery = "CREATE TABLE IF NOT EXISTS departments (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    department_name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)";
$conn->query($departmentsTableQuery);

// Update students table to add department_id if it doesn't exist
$checkDeptIdColumnQuery = "SHOW COLUMNS FROM students LIKE 'department_id'";
$deptIdColumnResult = $conn->query($checkDeptIdColumnQuery);
if ($deptIdColumnResult->num_rows == 0) {
    $addDeptIdColumnQuery = "ALTER TABLE students ADD COLUMN department_id INT(11) NULL";
    $conn->query($addDeptIdColumnQuery);
}

// Check and add profile_photo column if it doesn't exist
$checkPhotoColumnQuery = "SHOW COLUMNS FROM students LIKE 'profile_photo'";
$photoColumnExists = $conn->query($checkPhotoColumnQuery);
if ($photoColumnExists->num_rows === 0) {
    $addColumnQuery = "ALTER TABLE students ADD COLUMN profile_photo VARCHAR(255) NULL";
    $conn->query($addColumnQuery);
}

// Handle student deletion
if (isset($_GET['delete'])) {
    $studentId = $_GET['delete'];
    $deleteQuery = "DELETE FROM students WHERE id = ?";
    $stmt = $conn->prepare($deleteQuery);
    $stmt->bind_param("i", $studentId);
    
    if ($stmt->execute()) {
        $successMessage = "Student deleted successfully!";
    } else {
        $errorMessage = "Error deleting student: " . $conn->error;
    }
    $stmt->close();
}

// Get students with filtering by department, class, and session
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$itemsPerPage = 10;
$offset = ($page - 1) * $itemsPerPage;

// Search and filter parameters
$searchTerm = isset($_GET['search']) ? $_GET['search'] : '';
$departmentFilter = isset($_GET['department']) ? $_GET['department'] : '';
$classFilter = isset($_GET['class']) ? $_GET['class'] : '';
$sessionFilter = isset($_GET['session']) ? $_GET['session'] : '';

$whereClause = "";
$params = [];
$types = "";

if (!empty($searchTerm)) {
    $whereClause .= " WHERE (s.student_id LIKE ? OR s.first_name LIKE ? OR s.last_name LIKE ? OR s.email LIKE ?)";
    $searchParam = "%$searchTerm%";
    $params = [$searchParam, $searchParam, $searchParam, $searchParam];
    $types = "ssss";
}

if (!empty($departmentFilter)) {
    $whereClause = empty($whereClause) ? " WHERE s.department_id = ?" : $whereClause . " AND s.department_id = ?";
    $params[] = $departmentFilter;
    $types .= "i";
}

if (!empty($classFilter)) {
    $whereClause = empty($whereClause) ? " WHERE s.class_id = ?" : $whereClause . " AND s.class_id = ?";
    $params[] = $classFilter;
    $types .= "i";
}

if (!empty($sessionFilter)) {
    $whereClause = empty($whereClause) ? " WHERE s.session_id = ?" : $whereClause . " AND s.session_id = ?";
    $params[] = $sessionFilter;
    $types .= "i";
}

// Get total students count for pagination
$countQuery = "SELECT COUNT(*) as total FROM students s" . $whereClause;
$stmt = $conn->prepare($countQuery);
if (!empty($params)) {
    $stmt->bind_param($types, ...$params);
}
$stmt->execute();
$totalResult = $stmt->get_result();
$totalRows = $totalResult->fetch_assoc()['total'];
$totalPages = ceil($totalRows / $itemsPerPage);

// Get students for current page
$studentsQuery = "SELECT s.*, u.username, d.department_name, c.class_name, ss.session_name FROM students s
                LEFT JOIN users u ON s.user_id = u.id
                LEFT JOIN departments d ON s.department_id = d.id
                LEFT JOIN classes c ON s.class_id = c.id
                LEFT JOIN sessions ss ON s.session_id = ss.id" . 
                $whereClause . 
                " ORDER BY s.first_name, s.last_name
                LIMIT ? OFFSET ?";

$params[] = $itemsPerPage;
$params[] = $offset;
$types .= "ii";

$stmt = $conn->prepare($studentsQuery);
$stmt->bind_param($types, ...$params);
$stmt->execute();
$students = $stmt->get_result();

// Get departments for filter
$departmentsQuery = "SELECT * FROM departments ORDER BY department_name";
$departments = $conn->query($departmentsQuery);

// Get classes for filter
$classesQuery = "SELECT * FROM classes ORDER BY class_name";
$classes = $conn->query($classesQuery);

// Get sessions for filter
$sessionsQuery = "SELECT * FROM sessions ORDER BY session_name";
$sessions = $conn->query($sessionsQuery);
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>শিক্ষার্থী ব্যবস্থাপনা - কলেজ ম্যানেজমেন্ট সিস্টেম</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- All styles moved to admin.css file -->
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar">
                <div class="text-center mb-4">
                    <h3>অ্যাডমিন প্যানেল</h3>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i> ড্যাশবোর্ড
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="students.php">
                            <i class="fas fa-user-graduate me-2"></i> শিক্ষার্থী
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="teachers.php">
                            <i class="fas fa-chalkboard-teacher me-2"></i> শিক্ষক
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="staff.php">
                            <i class="fas fa-user-tie me-2"></i> কর্মচারী
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="courses.php">
                            <i class="fas fa-book me-2"></i> কোর্স
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="classes.php">
                            <i class="fas fa-chalkboard me-2"></i> ক্লাস
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="departments.php">
                            <i class="fas fa-building me-2"></i> বিভাগ
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="subjects.php">
                            <i class="fas fa-book-open me-2"></i> বিষয়
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="exams.php">
                            <i class="fas fa-file-alt me-2"></i> পরীক্ষা
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="results.php">
                            <i class="fas fa-chart-bar me-2"></i> ফলাফল
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="fees.php">
                            <i class="fas fa-money-bill-wave me-2"></i> ফি
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reports.php">
                            <i class="fas fa-file-pdf me-2"></i> রিপোর্ট
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="settings.php">
                            <i class="fas fa-cog me-2"></i> সেটিংস
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="users.php">
                            <i class="fas fa-users me-2"></i> ইউজার ম্যানেজমেন্ট
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="attendance.php">
                            <i class="fas fa-calendar-check me-2"></i> উপস্থিতি
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="library.php">
                            <i class="fas fa-book-reader me-2"></i> লাইব্রেরি
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="hostel.php">
                            <i class="fas fa-hotel me-2"></i> হোস্টেল
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="transport.php">
                            <i class="fas fa-bus me-2"></i> পরিবহন
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="events.php">
                            <i class="fas fa-calendar-day me-2"></i> ইভেন্ট
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="notices.php">
                            <i class="fas fa-bullhorn me-2"></i> নোটিশ
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="announcements.php">
                            <i class="fas fa-bell me-2"></i> ঘোষণা
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../includes/logout.inc.php">
                            <i class="fas fa-sign-out-alt me-2"></i> লগআউট
                        </a>
                    </li>
                </ul>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <div class="row mb-4">
                    <div class="col">
                        <h2>শিক্ষার্থী ব্যবস্থাপনা</h2>
                        <p class="text-muted">শিক্ষার্থীদের তথ্য দেখুন, যোগ করুন, সম্পাদনা করুন এবং মুছুন</p>
                    </div>
                    <div class="col-auto">
                        <a href="student_id_card.php" class="btn btn-success me-2">
                            <i class="fas fa-id-card me-2"></i>আইডি কার্ড প্রিন্ট
                        </a>
                        <a href="add_student.php" class="btn btn-primary">
                            <i class="fas fa-plus-circle me-2"></i>নতুন শিক্ষার্থী যোগ করুন
                        </a>
                    </div>
                </div>

                <!-- Quick Access Links -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header bg-light">
                                <h5 class="card-title mb-0">দ্রুত অ্যাক্সেস লিংক</h5>
                            </div>
                            <div class="card-body">
                                <div class="d-flex flex-wrap gap-2">
                                    <a href="departments.php" class="btn btn-outline-primary">
                                        <i class="fas fa-building me-2"></i>বিভাগ ব্যবস্থাপনা
                                    </a>
                                    <a href="classes.php" class="btn btn-outline-primary">
                                        <i class="fas fa-chalkboard me-2"></i>ক্লাস ব্যবস্থাপনা
                                    </a>
                                    <a href="sessions.php" class="btn btn-outline-primary">
                                        <i class="fas fa-calendar-alt me-2"></i>সেশন ব্যবস্থাপনা
                                    </a>
                                    <a href="student_subject_selection.php" class="btn btn-outline-primary">
                                        <i class="fas fa-list-check me-2"></i>শিক্ষার্থী বিষয় নির্বাচন
                                    </a>
                                    <a href="subject_selected_students.php" class="btn btn-outline-primary">
                                        <i class="fas fa-user-check me-2"></i>নির্বাচিত বিষয়কৃত ছাত্র/ছাত্রীর তালিকা
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <?php if (isset($successMessage)): ?>
                    <div class="alert alert-success" role="alert">
                        <?php echo $successMessage; ?>
                    </div>
                <?php endif; ?>

                <?php if (isset($errorMessage)): ?>
                    <div class="alert alert-danger" role="alert">
                        <?php echo $errorMessage; ?>
                    </div>
                <?php endif; ?>

                <!-- Search and Filter -->
                <div class="card mb-4">
                    <div class="card-body">
                        <form method="GET" action="students.php">
                            <div class="search-container">
                                <div class="flex-grow-1">
                                    <input type="text" name="search" class="form-control" placeholder="আইডি, নাম, বা ইমেইল দিয়ে খুঁজুন" value="<?php echo htmlspecialchars($searchTerm); ?>">
                                </div>
                                <div>
                                    <select name="department" class="form-select">
                                        <option value="">সব বিভাগ</option>
                                        <?php if ($departments && $departments->num_rows > 0): ?>
                                            <?php while ($dept = $departments->fetch_assoc()): ?>
                                                <option value="<?php echo $dept['id']; ?>" <?php echo ($departmentFilter === $dept['id']) ? 'selected' : ''; ?>>
                                                    <?php echo $dept['department_name']; ?>
                                                </option>
                                            <?php endwhile; ?>
                                        <?php endif; ?>
                                    </select>
                                </div>
                                <div>
                                    <select name="class" class="form-select">
                                        <option value="">সব ক্লাস</option>
                                        <?php if ($classes && $classes->num_rows > 0): ?>
                                            <?php while ($class = $classes->fetch_assoc()): ?>
                                                <option value="<?php echo $class['id']; ?>" <?php echo ($classFilter === $class['id']) ? 'selected' : ''; ?>>
                                                    <?php echo $class['class_name']; ?>
                                                </option>
                                            <?php endwhile; ?>
                                        <?php endif; ?>
                                    </select>
                                </div>
                                <div>
                                    <select name="session" class="form-select">
                                        <option value="">সব সেশন</option>
                                        <?php if ($sessions && $sessions->num_rows > 0): ?>
                                            <?php while ($session = $sessions->fetch_assoc()): ?>
                                                <option value="<?php echo $session['id']; ?>" <?php echo ($sessionFilter === $session['id']) ? 'selected' : ''; ?>>
                                                    <?php echo $session['session_name']; ?>
                                                </option>
                                            <?php endwhile; ?>
                                        <?php endif; ?>
                                    </select>
                                </div>
                                <div>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search me-2"></i>খুঁজুন
                                    </button>
                                    <?php if (!empty($searchTerm) || !empty($departmentFilter) || !empty($classFilter) || !empty($sessionFilter)): ?>
                                        <a href="students.php" class="btn btn-secondary ms-2">রিসেট</a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Students Table -->
                <div class="card">
                    <div class="card-body">
                        <!-- View Toggle Icons -->
                        <div class="d-flex justify-content-end mb-3">
                            <div class="btn-group" role="group" aria-label="View Options">
                                <button type="button" class="btn btn-outline-primary view-option" data-view="grid">
                                    <i class="fas fa-th-large"></i>
                                </button>
                                <button type="button" class="btn btn-outline-primary active view-option" data-view="list">
                                    <i class="fas fa-list"></i>
                                </button>
                                <button type="button" class="btn btn-outline-primary view-option" data-view="detail">
                                    <i class="fas fa-th-list"></i>
                                </button>
                            </div>
                        </div>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>ছবি</th>
                                        <th>শিক্ষার্থী আইডি</th>
                                        <th>নাম</th>
                                        <th>ইমেইল</th>
                                        <th>ফোন</th>
                                        <th>বিভাগ</th>
                                        <th>ক্লাস</th>
                                        <th>সেশন</th>
                                        <th>অ্যাকশন</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if ($students && $students->num_rows > 0): ?>
                                        <?php while ($student = $students->fetch_assoc()): ?>
                                            <tr>
                                                <td>
                                                    <?php if (!empty($student['profile_photo'])): ?>
                                                        <img src="../<?php echo $student['profile_photo']; ?>" alt="প্রোফাইল ছবি" class="rounded-circle" width="40" height="40" style="object-fit: cover;">
                                                    <?php else: ?>
                                                        <div class="rounded-circle bg-secondary text-white d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                                            <i class="fas fa-user"></i>
                                                        </div>
                                                    <?php endif; ?>
                                                </td>
                                                <td><?php echo $student['student_id']; ?></td>
                                                <td><?php echo $student['first_name'] . ' ' . $student['last_name']; ?></td>
                                                <td><?php echo $student['email']; ?></td>
                                                <td><?php echo $student['phone']; ?></td>
                                                <td><?php echo $student['department_name'] ?? 'N/A'; ?></td>
                                                <td><?php echo $student['class_name'] ?? 'N/A'; ?></td>
                                                <td><?php echo $student['session_name'] ?? 'N/A'; ?></td>
                                                <td class="table-actions">
                                                    <a href="view_student.php?id=<?php echo $student['student_id']; ?>" class="btn btn-sm btn-info">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="edit_student.php?id=<?php echo $student['id']; ?>" class="btn btn-sm btn-warning">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <a href="students.php?delete=<?php echo $student['id']; ?>" class="btn btn-sm btn-danger" onclick="return confirm('আপনি কি নিশ্চিত যে আপনি এই শিক্ষার্থীকে মুছে ফেলতে চান?')">
                                                        <i class="fas fa-trash"></i>
                                                    </a>
                                                </td>
                                            </tr>
                                        <?php endwhile; ?>
                                    <?php else: ?>
                                        <tr>
                                            <td colspan="10" class="text-center">কোন শিক্ষার্থী পাওয়া যায়নি</td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <?php if ($totalPages > 1): ?>
                            <nav>
                                <ul class="pagination justify-content-center">
                                    <li class="page-item <?php echo ($page <= 1) ? 'disabled' : ''; ?>">
                                        <a class="page-link" href="?page=<?php echo $page - 1; ?>&search=<?php echo urlencode($searchTerm); ?>&department=<?php echo urlencode($departmentFilter); ?>&class=<?php echo urlencode($classFilter); ?>&session=<?php echo urlencode($sessionFilter); ?>">
                                            পূর্ববর্তী
                                        </a>
                                    </li>
                                    
                                    <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                                        <li class="page-item <?php echo ($page == $i) ? 'active' : ''; ?>">
                                            <a class="page-link" href="?page=<?php echo $i; ?>&search=<?php echo urlencode($searchTerm); ?>&department=<?php echo urlencode($departmentFilter); ?>&class=<?php echo urlencode($classFilter); ?>&session=<?php echo urlencode($sessionFilter); ?>">
                                                <?php echo $i; ?>
                                            </a>
                                        </li>
                                    <?php endfor; ?>
                                    
                                    <li class="page-item <?php echo ($page >= $totalPages) ? 'disabled' : ''; ?>">
                                        <a class="page-link" href="?page=<?php echo $page + 1; ?>&search=<?php echo urlencode($searchTerm); ?>&department=<?php echo urlencode($departmentFilter); ?>&class=<?php echo urlencode($classFilter); ?>&session=<?php echo urlencode($sessionFilter); ?>">
                                            পরবর্তী
                                        </a>
                                    </li>
                                </ul>
                            </nav>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Get view toggle buttons
            const viewOptions = document.querySelectorAll('.view-option');
            const tableContainer = document.querySelector('.table-responsive');
            
            // Current active view
            let currentView = 'list'; // Default view
            
            // Add click event to all view options
            viewOptions.forEach(button => {
                button.addEventListener('click', function() {
                    const viewType = this.getAttribute('data-view');
                    
                    // Update active button
                    viewOptions.forEach(btn => btn.classList.remove('active'));
                    this.classList.add('active');
                    
                    // Handle view change
                    changeView(viewType);
                });
            });
            
            // Function to change the view
            function changeView(viewType) {
                if (viewType === currentView) return;
                
                currentView = viewType;
                
                // You can add custom view logic here based on the viewType
                // For now, we'll just show an alert
                if (viewType === 'grid') {
                    alert('গ্রিড ভিউ বৈশিষ্ট্য বর্তমানে বিকাশের অধীনে আছে।');
                } else if (viewType === 'detail') {
                    alert('বিস্তারিত ভিউ বৈশিষ্ট্য বর্তমানে বিকাশের অধীনে আছে।');
                }
                
                // Default to list view for now
                // In the future, you can implement actual view changes here
            }
        });
    </script>
</body>
</html> 