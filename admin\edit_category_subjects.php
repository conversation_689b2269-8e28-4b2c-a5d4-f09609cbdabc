<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Get category ID from URL
if (!isset($_GET['category_id']) || empty($_GET['category_id'])) {
    header("Location: subject_categories.php");
    exit();
}

$category_id = intval($_GET['category_id']);

// Get category details
$categoryQuery = "SELECT sc.*, d.department_name 
                  FROM subject_categories sc 
                  LEFT JOIN departments d ON sc.department_id = d.id 
                  WHERE sc.id = ?";
$stmt = $conn->prepare($categoryQuery);
$stmt->bind_param("i", $category_id);
$stmt->execute();
$categoryResult = $stmt->get_result();

if ($categoryResult->num_rows == 0) {
    header("Location: subject_categories.php");
    exit();
}

$category = $categoryResult->fetch_assoc();
$stmt->close();

// Handle form submissions
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['update_subjects'])) {
        $selected_subjects = $_POST['subject_ids'] ?? [];
        
        // First, remove all existing mappings for this category
        $deleteQuery = "DELETE FROM subject_category_mapping WHERE category_id = ?";
        $stmt = $conn->prepare($deleteQuery);
        $stmt->bind_param("i", $category_id);
        $stmt->execute();
        $stmt->close();
        
        // Add new mappings
        if (!empty($selected_subjects)) {
            $insertQuery = "INSERT INTO subject_category_mapping (subject_id, category_id, department_id) VALUES (?, ?, ?)";
            $stmt = $conn->prepare($insertQuery);
            
            $success_count = 0;
            foreach ($selected_subjects as $subject_id) {
                $stmt->bind_param("iii", $subject_id, $category_id, $category['department_id']);
                if ($stmt->execute()) {
                    $success_count++;
                }
            }
            $stmt->close();
            
            $message = "$success_count টি বিষয় সফলভাবে আপডেট করা হয়েছে!";
            $messageType = "success";
        } else {
            $message = "এই ক্যাটাগরি থেকে সব বিষয় সরানো হয়েছে!";
            $messageType = "warning";
        }
    }
}

// Get all subjects
$subjectsQuery = "SELECT s.*, d.department_name 
                  FROM subjects s 
                  LEFT JOIN departments d ON s.department_id = d.id 
                  WHERE s.is_active = 1 OR s.is_active IS NULL
                  ORDER BY d.department_name, s.subject_name";
$subjects = $conn->query($subjectsQuery);

// Get currently assigned subjects for this category
$assignedQuery = "SELECT subject_id FROM subject_category_mapping WHERE category_id = ?";
$stmt = $conn->prepare($assignedQuery);
$stmt->bind_param("i", $category_id);
$stmt->execute();
$assignedResult = $stmt->get_result();

$assignedSubjects = [];
while ($row = $assignedResult->fetch_assoc()) {
    $assignedSubjects[] = $row['subject_id'];
}
$stmt->close();

// Get all departments for filtering
$departmentsQuery = "SELECT * FROM departments ORDER BY department_name";
$departments = $conn->query($departmentsQuery);
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>বিষয় সংশোধন - <?php echo htmlspecialchars($category['category_name']); ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        * {
            font-family: 'Hind Siliguri', sans-serif !important;
        }
        
        body {
            background-color: #f8f9fa;
            margin: 0;
            padding: 20px;
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            margin-bottom: 20px;
        }
        
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            padding: 20px;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 10px 20px;
            font-weight: 600;
        }
        
        .btn-secondary {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
            border: none;
            border-radius: 10px;
            padding: 10px 20px;
            font-weight: 600;
        }
        
        .form-control, .form-select {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
            font-weight: 500;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .subject-item {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 10px;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .subject-item:hover {
            border-color: #667eea;
            background-color: #f8f9ff;
        }
        
        .subject-item.selected {
            border-color: #28a745;
            background-color: #d4edda;
        }
        
        .subject-item input[type="checkbox"] {
            transform: scale(1.2);
            margin-right: 10px;
        }
        
        .department-filter {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .badge {
            font-size: 0.8rem;
            padding: 6px 12px;
            border-radius: 20px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2><i class="fas fa-edit me-2"></i>বিষয় সংশোধন</h2>
                        <p class="text-muted mb-0">
                            ক্যাটাগরি: <strong><?php echo htmlspecialchars($category['category_name']); ?></strong>
                            <?php if ($category['department_name']): ?>
                                | বিভাগ: <strong><?php echo htmlspecialchars($category['department_name']); ?></strong>
                            <?php endif; ?>
                        </p>
                    </div>
                    <a href="subject_categories.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>ফিরে যান
                    </a>
                </div>
            </div>
        </div>

        <?php if (!empty($message)): ?>
            <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
                <?php echo $message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Main Form -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-list-check me-2"></i>বিষয় নির্বাচন করুন</h5>
            </div>
            <div class="card-body">
                <!-- Department Filter -->
                <div class="department-filter">
                    <div class="row align-items-center">
                        <div class="col-md-4">
                            <label for="dept_filter" class="form-label"><strong>বিভাগ অনুযায়ী ফিল্টার:</strong></label>
                            <select id="dept_filter" class="form-select" onchange="filterSubjectsByDepartment()">
                                <option value="">সব বিভাগ</option>
                                <?php
                                $departments->data_seek(0);
                                while($dept = $departments->fetch_assoc()): ?>
                                    <option value="<?php echo $dept['id']; ?>"><?php echo htmlspecialchars($dept['department_name']); ?></option>
                                <?php endwhile; ?>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label"><strong>দ্রুত নির্বাচন:</strong></label><br>
                            <button type="button" class="btn btn-outline-primary btn-sm me-2" onclick="selectAll()">
                                <i class="fas fa-check-double me-1"></i>সব নির্বাচন
                            </button>
                            <button type="button" class="btn btn-outline-secondary btn-sm" onclick="deselectAll()">
                                <i class="fas fa-times me-1"></i>সব বাতিল
                            </button>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label"><strong>নির্বাচিত:</strong></label><br>
                            <span id="selected_count" class="badge bg-primary">0 টি বিষয়</span>
                        </div>
                    </div>
                </div>

                <form method="POST">
                    <div class="row" id="subjects_container">
                        <?php
                        $subjects->data_seek(0);
                        while($subject = $subjects->fetch_assoc()):
                            $isSelected = in_array($subject['id'], $assignedSubjects);
                        ?>
                            <div class="col-md-6 col-lg-4 subject-wrapper" data-department="<?php echo $subject['department_id'] ?? ''; ?>">
                                <div class="subject-item <?php echo $isSelected ? 'selected' : ''; ?>" onclick="toggleSubject(this, <?php echo $subject['id']; ?>)">
                                    <input type="checkbox" name="subject_ids[]" value="<?php echo $subject['id']; ?>"
                                           <?php echo $isSelected ? 'checked' : ''; ?> onchange="updateSelectedCount()">
                                    <strong><?php echo htmlspecialchars($subject['subject_name']); ?></strong>
                                    <?php if ($subject['department_name']): ?>
                                        <br><small class="text-muted">
                                            <i class="fas fa-building me-1"></i><?php echo htmlspecialchars($subject['department_name']); ?>
                                        </small>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endwhile; ?>
                    </div>

                    <div class="mt-4 text-center">
                        <button type="submit" name="update_subjects" class="btn btn-primary btn-lg">
                            <i class="fas fa-save me-2"></i>বিষয় আপডেট করুন
                        </button>
                        <a href="subject_categories.php" class="btn btn-secondary btn-lg ms-3">
                            <i class="fas fa-times me-2"></i>বাতিল
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Toggle subject selection
        function toggleSubject(element, subjectId) {
            const checkbox = element.querySelector('input[type="checkbox"]');
            checkbox.checked = !checkbox.checked;

            if (checkbox.checked) {
                element.classList.add('selected');
            } else {
                element.classList.remove('selected');
            }

            updateSelectedCount();
        }

        // Update selected count
        function updateSelectedCount() {
            const checkboxes = document.querySelectorAll('input[name="subject_ids[]"]:checked');
            const count = checkboxes.length;
            document.getElementById('selected_count').textContent = count + ' টি বিষয়';
        }

        // Select all visible subjects
        function selectAll() {
            const visibleCheckboxes = document.querySelectorAll('.subject-wrapper:not([style*="display: none"]) input[type="checkbox"]');
            visibleCheckboxes.forEach(checkbox => {
                checkbox.checked = true;
                checkbox.closest('.subject-item').classList.add('selected');
            });
            updateSelectedCount();
        }

        // Deselect all subjects
        function deselectAll() {
            const checkboxes = document.querySelectorAll('input[name="subject_ids[]"]');
            checkboxes.forEach(checkbox => {
                checkbox.checked = false;
                checkbox.closest('.subject-item').classList.remove('selected');
            });
            updateSelectedCount();
        }

        // Filter subjects by department
        function filterSubjectsByDepartment() {
            const departmentId = document.getElementById('dept_filter').value;
            const subjectWrappers = document.querySelectorAll('.subject-wrapper');

            subjectWrappers.forEach(wrapper => {
                const subjectDepartment = wrapper.getAttribute('data-department');

                if (departmentId === '' || departmentId === subjectDepartment || subjectDepartment === '') {
                    wrapper.style.display = '';
                } else {
                    wrapper.style.display = 'none';
                }
            });

            updateSelectedCount();
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            updateSelectedCount();

            // Prevent checkbox click from triggering parent click
            const checkboxes = document.querySelectorAll('input[type="checkbox"]');
            checkboxes.forEach(checkbox => {
                checkbox.addEventListener('click', function(e) {
                    e.stopPropagation();
                });
            });
        });
    </script>
</body>
</html>
